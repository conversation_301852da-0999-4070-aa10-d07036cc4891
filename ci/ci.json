{"$schema": "https://gist.githubusercontent.com/RexSkz/6997a097219bf6aba4a97ae5a907177d/raw/b8c822039ec167e8e21b4121fa891223a3023751/ci-json-schema.json", "service_name": "moego-svc-order-v2", "slack": ["#team-backend"], "language": {"type": "go", "version": "1.23"}, "install": {"commands": ["go mod download"]}, "lint": {"commands": ["curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/HEAD/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.3.1", "golangci-lint run -v --allow-parallel-runners --timeout 3m"]}, "test": {"commands": ["mkdir -p output", "go test -coverprofile output/cover.out -coverpkg ./internal/service/... -v ./internal/... 2>&1 | go-junit-report -set-exit-code -iocopy -out output/report.xml", "gocov convert output/cover.out | gocov-xml > output/cover.xml"], "report": "./output/report.xml", "coverage": "./output/cover.xml"}, "build": {"commands": ["CGO_ENABLED=0 go build -o moego-svc-order-v2 ./internal/"], "build_image": [{"dockerfile": "./ci/Dockerfile", "context": "./"}]}, "deploy": {"type": "service"}}