secrets:
  - name: "datasource"
    prefix: "ds."
  - name: "mq"
    prefix: "mq."
  - name: "redis"
    prefix: "redis."
  - name: "aws"
    prefix: "aws."
  - name: "growthbook"
    prefix: "growthbook."

orderDsn: postgresql://${ds.postgres.moego_order.username}:${ds.postgres.moego_order.password}@${ds.postgres.url.master}:${ds.postgres.port}/moego_order?sslmode=disable
paymentDsn: ${ds.mysql.username}:${ds.mysql.password}@tcp(${ds.mysql.url.master}:${ds.mysql.port})/moe_payment?parseTime=true
eventBus:
  brokers:
    - ${mq.kafka.broker_url_0}
    - ${mq.kafka.broker_url_1}
    - ${mq.kafka.broker_url_2}
  consumers:
    payment:
      topic: moego.payment
      groupID: order
    appointment:
      topic: moego.erp.appointment
      groupID: order
    order:
      topic: moego.order
      groupID: order
    orderTips:
      topic: moego.order
      groupID: order_tips
    fulfillment:
      topic: moego.fulfillment
      groupID: order
  credential:
    region: ${aws.region}
    access_key_id: ${aws.access_key_id}
    secret_access_key: ${aws.secret_access_key}
redis:
  addr: ${redis.host}:${redis.port}
  password: ${redis.password}
  tls: ${redis.tls}
  insecureSkipVerify: false
growthBook:
  host: ${growthbook.host}
  clientKey: ${growthbook.client_key}
  interval: 10s