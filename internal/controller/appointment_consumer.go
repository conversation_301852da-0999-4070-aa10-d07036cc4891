package controller

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type AppointmentConsumer struct {
	*BaseEventConsumer
	depositOrderSvc service.DepositOrderService
	cli             marketingsvcpb.DiscountCodeServiceClient
}

func newAppointmentConsumer(
	conf *config.EventBusConfig,
	orderSvc service.OrderService,
	refundOrderSvc service.RefundOrderService,
	depositOrderSvc service.DepositOrderService,
	cli marketingsvcpb.DiscountCodeServiceClient,
) *eventbus.Consumer {
	ac := &AppointmentConsumer{
		BaseEventConsumer: NewBaseEventConsumer(orderSvc, refundOrderSvc),
		depositOrderSvc:   depositOrderSvc,
		cli:               cli,
	}

	consumer, err := eventbus.NewConsumer(
		conf.AppointmentConsumer(),
		ac.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new appointment consumer failed", zap.Error(err))
	}

	return consumer
}

func (ac *AppointmentConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	_ *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	return ac.HandleEventWithPanicRecovery(ctx, "AppointmentConsumer", func() error {
		switch eventType {
		case eventbuspb.EventType_APPOINTMENT_CANCELED:
			return ac.handleAppointmentCanceledEvent(ctx, eventID, eventData, eventType)
		default:
			zlog.Default().Debug(
				"skip not interested event",
				zap.String("consumer", "AppointmentConsumer"),
				zap.String("eventID", eventID),
				zap.Stringer("eventType", eventType),
			)

			return nil
		}
	})
}

func (ac *AppointmentConsumer) handleAppointmentCanceledEvent(
	ctx context.Context, eventID string, eventData *eventbuspb.EventData, eventType eventbuspb.EventType,
) error {
	event := eventData.GetAppointmentCanceledEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", "AppointmentConsumer"),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Int64("appointmentID", event.GetId()),
	)

	zlog.Info(handlerCtx, "received appointment canceled event")

	// 根据 AppointmentID 获取到所有关联的订单详情，并过滤掉其中的 noshow 类型订单.
	orders, err := ac.BaseEventConsumer.orderSvc.ListDetailByAppointment(ctx, event.GetId())
	if err != nil {
		zlog.Error(handlerCtx, "failed to list detailed orders by appointment ID", zap.Error(err))
		return err
	}

	// 不需要对 noshow 的订单进行处理.
	orders = ac.FilterNonNoShowOrders(orders)

	if len(orders) == 0 {
		zlog.Info(handlerCtx, "after filter, no orders need to be canceled")
		return nil
	}

	if !event.GetRefundDeposit() {
		orders = lo.Filter(
			orders,
			func(order *model.OrderDetail, _ int) bool { return !order.IsDeposit() },
		)
	}

	if err := ac.CancelProcessingOrders(handlerCtx, orders, model.OrderCancelReasonAppointmentCanceled); err != nil {
		zlog.Error(handlerCtx, "failed to cancel processing orders", zap.Error(err))
		return err
	}

	zlog.Info(handlerCtx, "successfully canceled processing orders")

	if !event.GetAutoRefundOrder() {
		zlog.Info(handlerCtx, "auto refund order is disabled, skip fully refund orders")
		return nil
	}

	if err := ac.FullyRefundOrders(handlerCtx, orders, model.OrderCancelReasonAppointmentCanceled); err != nil {
		zlog.Error(handlerCtx, "failed to fully refund orders", zap.Error(err))
		return err
	}

	zlog.Info(handlerCtx, "successfully fully refund orders")

	if err := ac.returnDiscount(handlerCtx, event.GetId()); err != nil {
		zlog.Error(
			handlerCtx, "failed to return discount code",
			zap.Int64("sourceID", event.GetId()),
			zap.Error(err),
		)

		return err
	}

	zlog.Info(
		handlerCtx, "successfully return discount code",
		zap.Int64("sourceID", event.GetId()),
	)

	return nil
}

func (ac *AppointmentConsumer) returnDiscount(ctx context.Context, sourceID int64) error {
	_, err := ac.cli.DeleteDiscountCodeLog(
		ctx, &marketingsvcpb.DeleteDiscountCodeLogInput{
			RedeemId: sourceID,
		},
	)

	return err
}
