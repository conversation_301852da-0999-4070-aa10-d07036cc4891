//nolint:dupl // 允许与 fulfillment_consumer.go 中的方法重复，保持代码结构独立性
package controller

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type AppointmentConsumer struct {
	orderSvc        service.OrderService
	refundOrderSvc  service.RefundOrderService
	depositOrderSvc service.DepositOrderService

	cli marketingsvcpb.DiscountCodeServiceClient
}

func newAppointmentConsumer(
	conf *config.EventBusConfig,
	orderSvc service.OrderService,
	refundOrderSvc service.RefundOrderService,
	depositOrderSvc service.DepositOrderService,
	cli marketingsvcpb.DiscountCodeServiceClient,
) *eventbus.Consumer {
	ac := &AppointmentConsumer{
		orderSvc:        orderSvc,
		refundOrderSvc:  refundOrderSvc,
		depositOrderSvc: depositOrderSvc,
		cli:             cli,
	}

	consumer, err := eventbus.NewConsumer(
		conf.AppointmentConsumer(),
		ac.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new appointment consumer failed", zap.Error(err))
	}

	return consumer
}

func (ac *AppointmentConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	_ *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	defer func() {
		if err := recover(); err != nil {
			zlog.Error(ctx, "Panic in AppointmentConsumer", zap.Any("err", err))
		}
	}()

	switch eventType {
	case eventbuspb.EventType_APPOINTMENT_CANCELED:
		return ac.handleAppointmentCanceledEvent(ctx, eventID, eventData, eventType)
	default:
		zlog.Default().Debug(
			"skip not interested event",
			zap.String("consumer", "AppointmentConsumer"),
			zap.String("eventID", eventID),
			zap.Stringer("eventType", eventType),
		)

		return nil
	}
}

func (ac *AppointmentConsumer) handleAppointmentCanceledEvent(
	ctx context.Context, eventID string, eventData *eventbuspb.EventData, eventType eventbuspb.EventType,
) error {
	event := eventData.GetAppointmentCanceledEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", "AppointmentConsumer"),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Int64("appointmentID", event.GetId()),
	)

	zlog.Info(handlerCtx, "received appointment canceled event")

	// 根据 AppointmentID 获取到所有关联的订单详情，并过滤掉其中的 noshow 类型订单.
	orders, err := ac.orderSvc.ListDetailByAppointment(ctx, event.GetId())
	if err != nil {
		zlog.Error(handlerCtx, "failed to list detailed orders by appointment ID", zap.Error(err))
		return err
	}

	// 不需要对 noshow 的订单进行处理.
	orders = lo.Filter(
		orders,
		func(order *model.OrderDetail, _ int) bool { return !order.Order.IsNoShow() },
	)

	if len(orders) == 0 {
		zlog.Info(handlerCtx, "after filter, no orders need to be canceled")
		return nil
	}

	if !event.GetRefundDeposit() {
		orders = lo.Filter(
			orders,
			func(order *model.OrderDetail, _ int) bool { return !order.IsDeposit() },
		)
	}

	if err := ac.cancelProcessingOrders(handlerCtx, orders); err != nil {
		zlog.Error(handlerCtx, "failed to cancel processing orders", zap.Error(err))
		return err
	}

	zlog.Info(handlerCtx, "successfully canceled processing orders")

	if !event.GetAutoRefundOrder() {
		zlog.Info(handlerCtx, "auto refund order is disabled, skip fully refund orders")
		return nil
	}

	if err := ac.fullyRefundOrders(handlerCtx, orders); err != nil {
		zlog.Error(handlerCtx, "failed to fully refund orders", zap.Error(err))
		return err
	}

	zlog.Info(handlerCtx, "successfully fully refund orders")

	if err := ac.returnDiscount(handlerCtx, event.GetId()); err != nil {
		zlog.Error(
			handlerCtx, "failed to return discount code",
			zap.Int64("sourceID", event.GetId()),
			zap.Error(err),
		)

		return err
	}

	zlog.Info(
		handlerCtx, "successfully return discount code",
		zap.Int64("sourceID", event.GetId()),
	)

	return nil
}

func (ac *AppointmentConsumer) cancelProcessingOrders(ctx context.Context, orders []*model.OrderDetail) error {
	// 使用 lo.Filter 过滤掉 noshow 类型的订单和已经进入终态的订单.
	processingOrders := lo.Filter(
		orders,
		func(order *model.OrderDetail, _ int) bool {
			return !order.Order.IsNoShow() && !order.Order.IsFinalStatus()
		},
	)

	zlog.Info(
		ctx,
		"found processing orders need to be canceled",
		zap.Int("count", len(processingOrders)),
	)

	if len(processingOrders) == 0 {
		zlog.Info(ctx, "no processing orders need to be canceled")
		return nil
	}

	for _, order := range processingOrders {
		ol := zlog.FromContext(ctx).With(
			zap.Int64("orderID", order.Order.ID),
			zap.Stringer("orderVersion", order.OrderVersion()),
		)

		if err := ac.orderSvc.CancelOrder(ctx, order.Order.ID, model.OrderCancelReasonAppointmentCanceled); err != nil {
			ol.Error("failed to cancel order", zap.Error(err))
			return err
		}

		ol.Info("order canceled")
	}

	return nil
}

// fullyRefundOrders 为所有传入的订单发起全额退款. 如果已经有退款的, 会按照残值发起新的退款来实现全额退款.
// 与用户发起的退款一样, 本方法认为只要成功创建 RefundOrder 即为成功, 不会等到实际退款完成.
func (ac *AppointmentConsumer) fullyRefundOrders(
	ctx context.Context,
	orderDetails []*model.OrderDetail,
) error {
	for _, order := range orderDetails {
		logger := zlog.FromContext(ctx).With(zap.Int64("orderID", order.Order.ID))

		if len(order.OrderPayments) == 0 {
			logger.Warn("order has no payments, skipping refund")
			continue
		}

		// 跳过已经退过款的单（不包括 deposit 单）.
		if order.Order.GetRefundedAmount().IsPositive() && !order.IsDeposit() {
			logger.Info(
				"order has been refunded, skipping auto fully refund",
				zap.Stringer("refundedAmount", order.Order.GetRefundedAmount()),
			)

			continue
		}

		// 跳过不需要退款的单.
		if !order.Order.GetRefundableAmount().IsPositive() {
			logger.Info(
				"order doesn't have refundable amount, skipping auto fully refund",
				zap.Stringer("refundableAmount", order.Order.GetRefundableAmount()),
			)

			continue
		}

		isDeductedDepositOrder, err := ac.isDeductedDepositOrder(ctx, order)
		if err != nil {
			logger.Error("failed to check if order is deducted deposit order", zap.Error(err))
			return err
		}

		req := ac.buildFullyRefundRequest(order, isDeductedDepositOrder)
		if _, err := ac.refundOrderSvc.RefundOrder(ctx, order, req, ac.orderSvc); err != nil {
			logger.Error("failed to fully refund order", zap.Error(err))
			return err
		}

		logger.Info("successfully fully refund order")
	}

	return nil
}

func (ac *AppointmentConsumer) isDeductedDepositOrder(ctx context.Context, order *model.OrderDetail) (bool, error) {
	detail, err := ac.refundOrderSvc.GetDepositDetailForRefund(ctx, order.Order)
	if err != nil {
		return false, err
	}

	if detail == nil {
		return false, nil
	}

	return detail.IsDeducted(), nil
}

// buildFullyRefundRequest 构建全额退款请求.
func (ac *AppointmentConsumer) buildFullyRefundRequest(
	order *model.OrderDetail, isDeductedDepositOrder bool,
) *ordersvcpb.RefundOrderRequest {
	req := &ordersvcpb.RefundOrderRequest{
		OrderId:      order.GetID(),
		RefundReason: model.OrderCancelReasonAppointmentCanceled,
	}

	// 考虑到存在关单前的退款，这里过滤一下.
	orderPayments := lo.Filter(
		order.OrderPayments, func(op *model.OrderPayment, _ int) bool {
			return op.GetRefundableAmount().IsPositive()
		},
	)

	req.SourceOrderPayments = lo.Map(
		orderPayments,
		func(op *model.OrderPayment, _ int) *ordersvcpb.RefundOrderRequest_OrderPayment {
			return &ordersvcpb.RefundOrderRequest_OrderPayment{Id: op.ID}
		},
	)

	// 优先使用 By Item 模式进行退款。
	// 有两个特例：
	// 1. byItem 无法处理 apply pkg 的情况，需要使用 byPayment 模式退款.
	// 2. Deposit order 被抵扣后，sales order 没有支付，需要使用 byPayment 模式退 deposit order.
	if !order.IsAppliedPackage() &&
		!isDeductedDepositOrder &&
		lo.Contains(order.Order.RefundableModes, orderpb.RefundMode_REFUND_MODE_BY_ITEM) {
		req.RefundMode = orderpb.RefundMode_REFUND_MODE_BY_ITEM
		req.RefundBy = &ordersvcpb.RefundOrderRequest_RefundByItem_{
			RefundByItem: &ordersvcpb.RefundOrderRequest_RefundByItem{
				RefundItems: lo.Map(
					order.OrderItems,
					func(oi *model.OrderItem, _ int) *ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem {
						return oi.BuildFullyRefundParam()
					},
				),
			},
		}

		return req
	}

	req.RefundMode = orderpb.RefundMode_REFUND_MODE_BY_PAYMENT
	req.RefundBy = &ordersvcpb.RefundOrderRequest_RefundByPayment_{
		RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
			OrderPaymentIds: lo.Map(
				req.GetSourceOrderPayments(),
				func(op *ordersvcpb.RefundOrderRequest_OrderPayment, _ int) int64 { return op.GetId() },
			),
			RefundAmount: money.FromDecimal(order.Order.GetRefundableAmount(), order.Order.CurrencyCode),
			RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
				IsConvenienceFeeIncluded: true, // 直接传入的可退总金额，是包含了 CV Fee 的.
			},
		},
	}

	return req
}

func (ac *AppointmentConsumer) returnDiscount(ctx context.Context, sourceID int64) error {
	_, err := ac.cli.DeleteDiscountCodeLog(
		ctx, &marketingsvcpb.DeleteDiscountCodeLogInput{
			RedeemId: sourceID,
		},
	)

	return err
}
