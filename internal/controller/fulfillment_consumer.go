package controller

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

// FulfillmentConsumer 消费履约相关事件。
type FulfillmentConsumer struct {
	*BaseEventConsumer
	cli marketingsvcpb.DiscountCodeServiceClient
}

func newFulfillmentConsumer(
	conf *config.EventBusConfig,
	orderSvc service.OrderService,
	refundOrderSvc service.RefundOrderService,
	cli marketingsvcpb.DiscountCodeServiceClient,
) *eventbus.Consumer {
	fc := &FulfillmentConsumer{
		BaseEventConsumer: NewBaseEventConsumer(orderSvc, refundOrderSvc),
		cli:               cli,
	}

	ebConf := conf.FulfillmentConsumer()

	consumer, err := eventbus.NewConsumer(
		ebConf,
		fc.handleEvent,
	)
	if err != nil {
		zlog.Default().Fatal("new fulfillment consumer failed", zap.Error(err))
	}

	return consumer
}

func (fc *FulfillmentConsumer) handleEvent(
	ctx context.Context,
	eventID string,
	_ *timestamppb.Timestamp,
	eventType eventbuspb.EventType,
	eventData *eventbuspb.EventData,
) error {
	return fc.HandleEventWithPanicRecovery(ctx, "FulfillmentConsumer", func() error {
		switch eventType {
		case eventbuspb.EventType_FULFILLMENT_CANCELED:
			return fc.handleFulfillmentCanceledEvent(ctx, eventID, eventData, eventType)
		default:
			zlog.Default().Debug(
				"skip not interested event",
				zap.String("consumer", "FulfillmentConsumer"),
				zap.String("eventID", eventID),
				zap.Stringer("eventType", eventType),
			)

			return nil
		}
	})
}

func (fc *FulfillmentConsumer) handleFulfillmentCanceledEvent(
	ctx context.Context, eventID string, eventData *eventbuspb.EventData, eventType eventbuspb.EventType,
) error {
	event := eventData.GetFulfillmentCanceledEvent()
	handlerCtx := zlog.NewContext(
		ctx,
		zap.String("consumer", "FulfillmentConsumer"),
		zap.String("eventID", eventID),
		zap.Stringer("eventType", eventType),
		zap.Int64("fulfillmentID", event.GetId()),
	)
	zlog.Info(handlerCtx, "received fulfillment canceled event")

	orders, err := fc.BaseEventConsumer.orderSvc.ListDetailByFulfillment(ctx, event.GetId())
	if err != nil {
		zlog.Error(handlerCtx, "failed to list detailed orders by fulfillment ID", zap.Error(err))
		return err
	}

	// 不需要对 noshow 的订单进行处理
	orders = fc.FilterNonNoShowOrders(orders)
	if len(orders) == 0 {
		zlog.Info(handlerCtx, "after filter, no orders need to be canceled")
		return nil
	}

	if err := fc.CancelProcessingOrders(handlerCtx, orders, model.OrderCancelReasonFulfillmentCanceled); err != nil {
		zlog.Error(handlerCtx, "failed to cancel processing orders", zap.Error(err))
		return err
	}

	zlog.Info(handlerCtx, "successfully canceled processing orders")

	if !event.GetAutoRefundOrder() {
		zlog.Info(handlerCtx, "auto refund order is disabled, skip fully refund orders")
		return nil
	}

	if err := fc.FullyRefundOrders(handlerCtx, orders, model.OrderCancelReasonFulfillmentCanceled); err != nil {
		zlog.Error(handlerCtx, "failed to fully refund orders", zap.Error(err))
		return err
	}

	zlog.Info(handlerCtx, "successfully fully refund orders")

	// Fulfillment 暂时不支持 discount（see RedeemType），所以不需要 return discount code

	return nil
}








