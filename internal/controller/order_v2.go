package controller

import (
	"context"
	"strings"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	money2 "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	ordersvcpb2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/core"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type OrderServerV2 struct {
	ordersvcpb2.UnimplementedOrderServiceServer

	promotionService service.PromotionService
	depositService   service.DepositOrderService
	orderService     service.OrderService
}

func NewOrderServerV2(
	promotionService service.PromotionService,
	depositService service.DepositOrderService,
	orderService service.OrderService,
) ordersvcpb2.OrderServiceServer {
	return &OrderServerV2{
		promotionService: promotionService,
		depositService:   depositService,
		orderService:     orderService,
	}
}

func (svr *OrderServerV2) PreviewCreateOrder(ctx context.Context, req *ordersvcpb2.PreviewCreateOrderRequest) (
	*ordersvcpb2.PreviewCreateOrderResponse, error,
) {
	engine, err := svr.buildOrderEngineForCreate(ctx, req)
	if err != nil {
		return nil, err
	}

	// 构造 Promotion 相关参数.
	promotions, err := svr.promotionService.PreviewCoupons(
		ctx,
		req.GetAutoApplyPromotions(),
		req.GetItems(),
		req.GetAppliedPromotions(),
		req.GetCustomerId(),
	)
	if err != nil {
		return nil, err
	}

	previewOrderDetail := engine.
		WithPromotion(promotions).
		PreviewOrderDetail()

	return &ordersvcpb2.PreviewCreateOrderResponse{
		Order: previewOrderDetail.ToPB(),
		AppliedPromotions: &ordersvcpb2.PreviewCreateOrderRequest_AppliedPromotions{
			Promotions: lo.Map(
				previewOrderDetail.OrderPromotions, func(
					op *model.OrderPromotion, _ int,
				) *ordersvcpb2.PreviewCreateOrderRequest_Promotion {
					return op.ToCreateReqPromotionPB()
				},
			),
		},
	}, nil
}

func (svr *OrderServerV2) CreateOrder(ctx context.Context, req *ordersvcpb2.CreateOrderRequest) (
	*ordersvcpb2.CreateOrderResponse, error,
) {
	engine, err := svr.buildOrderEngineForCreate(ctx, req)
	if err != nil {
		return nil, err
	}

	promotions, err := svr.promotionService.PreviewCoupons(
		ctx,
		false,
		req.GetItems(),
		req.GetAppliedPromotions(),
		req.GetCustomerId(),
	)
	if err != nil {
		return nil, err
	}

	orderDetail := engine.
		WithPromotion(promotions).
		PreviewOrderDetail()

	// 用于 Pay online, 原本是在查询 GUID 的时候才生成，这里调整为创建的时候直接生成.
	orderDetail.Order.GUID = strings.ReplaceAll(uuid.New().String(), "-", "")
	orderDetail.Order.CreateBy = req.GetStaffId()
	orderDetail.Order.UpdateBy = req.GetStaffId()
	orderDetail.Order.Title = req.GetTitle()
	orderDetail.Order.Description = req.GetDescription()

	// Order Ref ID.
	refID, err := svr.orderService.CreateInvoiceID(
		ctx, &ordersvcpb.CreateInvoiceIDRequest{
			SourceType: req.GetSourceType(),
			SourceId:   req.GetSourceId(),
			CompanyId:  req.GetCompanyId(),
			BusinessId: req.GetBusinessId(),
			StaffId:    req.GetStaffId(),
			CustomerId: req.GetCustomerId(),
		},
	)
	if err != nil {
		return nil, err
	}

	orderDetail.Order.OrderRefID = refID

	orderDetail, err = svr.orderService.CreateOrder(ctx, orderDetail)
	if err != nil {
		return nil, err
	}

	// 异步调用一次 redeem promotions.
	svr.promotionService.AsyncRedeemCoupons(ctx, &model.RedeemPromotionParams{
		OrderID:          orderDetail.GetID(),
		CustomerID:       orderDetail.GetCustomerID(),
		AppointmentID:    orderDetail.GetAppointmentID(),
		OrderTotalAmount: orderDetail.GetTotalAmount(),
		OrderPromotions:  orderDetail.OrderPromotions,
	})

	return &ordersvcpb2.CreateOrderResponse{
		Order: orderDetail.ToPB(),
	}, nil
}

type createOrderSource interface {
	GetItems() []*ordersvcpb2.PreviewCreateOrderRequest_CartItem
	GetTipsAmount() *money2.Money
	GetAppliedPromotions() *ordersvcpb2.PreviewCreateOrderRequest_AppliedPromotions
	GetSourceType() orderpb.OrderSourceType
	GetSourceId() int64
	GetBusinessId() int64
	GetCompanyId() int64
	GetCustomerId() int64
}

func (svr *OrderServerV2) buildOrderEngineForCreate(ctx context.Context, src createOrderSource) (
	*core.OrderEngine, error,
) {
	// 预处理 Rate.
	taxIDToRate, err := svr.validateItemTax(src.GetItems())
	if err != nil {
		return nil, err
	}

	currencyCode := src.GetTipsAmount().GetCurrencyCode()

	previewItems := lo.Map(
		src.GetItems(), func(it *ordersvcpb2.PreviewCreateOrderRequest_CartItem, idx int) *model.OrderItem {
			return &model.OrderItem{
				ID:           int64(idx) + 1, // Fake OrderItemID.
				OrderID:      0,
				BusinessID:   src.GetBusinessId(),
				StaffID:      it.GetStaffId(),
				StaffIDs:     nil,
				ItemType:     model.FormatOrderItemType(it.GetItemType()),
				ObjectID:     it.GetItemId(),
				ExternalUUID: it.GetExternalUuid(),
				Name:         it.GetName(),
				Description:  it.GetDescription(),
				UnitPrice:    money.ToDecimal(it.GetUnitPrice()),
				SubTotalItems: lo.Map(
					it.GetSubtotalDetail().GetPriceItems(),
					func(pi *orderpb.PriceDetailModel_PriceItem, _ int) *model.PriceItem {
						return &model.PriceItem{
							Name:         pi.GetName(),
							UnitPrice:    money.ToDecimal(pi.GetUnitPrice()),
							CurrencyCode: pi.GetUnitPrice().GetCurrencyCode(),
							Quantity:     pi.GetQuantity(),
							Operator:     pi.GetOperator(),
						}
					},
				),
				Quantity: it.GetQuantity(),
				// PurchasedQuantity 应当在 Apply Promotion 的相关流程中计算.
				PurchasedQuantity: 0,
				PetID:             it.GetPetId(),
				CurrencyCode:      currencyCode,
				Tax: model.Tax{
					ID:     it.GetTax().GetId(),
					Name:   it.GetTax().GetName(),
					Rate:   taxIDToRate[it.GetTax().GetId()],
					Amount: decimal.Zero,
				},
				// 除了 Unit Price 和 SubTotalItems 之外，所有金额字段都应该由 Engine 计算.
			}
		},
	)

	// 构造 Order.
	previewOrder := model.NewEmptyOrder(
		orderpb.OrderModel_ORIGIN, // Preview Create Order 永远的都是 Sales Order.
		src.GetSourceType(), src.GetSourceId(),
		src.GetCompanyId(), src.GetBusinessId(), src.GetCustomerId(),
		0, // staffID 在 CreateOrder 里面填入.
	)
	previewOrder.CurrencyCode = currencyCode
	// Preview 流程固定为 Immutable Order.
	previewOrder.OrderVersion = model.OrderVersionImmutableOrder

	// Deposit.
	depositDetail, err := svr.getDepositDetailBySource(
		ctx, src.GetSourceType(), src.GetSourceId(),
	)
	if err != nil {
		return nil, err
	}

	engine := core.NewOrderEngine(previewOrder, previewItems).
		WithDepositDetail(depositDetail).
		WithTipAmount(money.ToDecimal(src.GetTipsAmount()))

	return engine, nil
}

func (svr *OrderServerV2) getDepositDetailBySource(
	ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64,
) (
	*model.DepositDetail, error,
) {
	if sourceType != orderpb.OrderSourceType_APPOINTMENT || sourceID <= 0 {
		return nil, nil
	}

	orders, err := svr.orderService.ListByAppointment(ctx, sourceID)
	if err != nil {
		return nil, err
	}

	depositOrder, ok := lo.Find(
		orders,
		func(order *model.Order) bool { return order.OrderType == orderpb.OrderModel_DEPOSIT },
	)
	if !ok {
		return nil, nil
	}

	depositDetail, err := svr.depositService.GetDepositDetail(ctx, depositOrder)
	if err != nil {
		return nil, err
	}

	return depositDetail, nil
}

func (svr *OrderServerV2) validateItemTax(items []*ordersvcpb2.PreviewCreateOrderRequest_CartItem) (
	map[int64]decimal.Decimal, error,
) {
	const taxFree = "0.00"

	taxIDToRate := make(map[int64]decimal.Decimal)

	for _, it := range items {
		taxID := it.GetTax().GetId()
		if _, ok := taxIDToRate[taxID]; ok {
			continue
		}

		rateStr := it.GetTax().GetRate().GetValue()
		if rateStr == "" || taxID == 0 {
			rateStr = taxFree
		}

		rateDec, err := decimal.NewFromString(rateStr)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "item[%s]: invalid tax rate: '%s'", it.GetName(), err)
		}

		if rateDec.IsNegative() {
			return nil, status.Errorf(codes.InvalidArgument, "item[%s]: tax rate must not be negative", it.GetName())
		}

		taxIDToRate[taxID] = rateDec
	}

	return taxIDToRate, nil
}
