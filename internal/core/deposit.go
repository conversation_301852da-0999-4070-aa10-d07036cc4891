package core

import (
	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func (oe *OrderEngine) WithDepositDetail(depositDetail *model.DepositDetail) *OrderEngine {
	oe.depositDetail = depositDetail

	return oe
}

func (oe *OrderEngine) calculateDepositDeductedAmount(totalAmount decimal.Decimal) (
	decimal.Decimal,
	*model.DepositChangeLog,
) {
	if oe.depositDetail == nil || oe.depositDetail.LatestChangeLog == nil {
		return decimal.Zero, nil
	}

	balance := oe.depositDetail.LatestChangeLog.Balance
	if balance.IsZero() {
		return decimal.Zero, nil
	}

	deductedAmount := totalAmount
	if deductedAmount.GreaterThan(balance) {
		deductedAmount = balance
	}

	return deductedAmount, &model.DepositChangeLog{
		DepositOrderID: oe.depositDetail.LatestChangeLog.DepositOrderID,
		ChangeType:     orderpb.DepositChangeType_DECREASE,
		Reason:         orderpb.DepositChangeReason_DEDUCTION,
		ChangedAmount:  deductedAmount,
		Balance:        balance.Sub(deductedAmount),
		CurrencyCode:   oe.order.CurrencyCode,
		PreviousLogID:  oe.depositDetail.LatestChangeLog.ID,
		CompanyID:      oe.order.CompanyID,
		BusinessID:     oe.order.BusinessID,
		CustomerID:     oe.order.CustomerID,
		StaffID:        oe.order.CreateBy,
	}
}
