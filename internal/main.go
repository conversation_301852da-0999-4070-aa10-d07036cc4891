package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/DataDog/dd-trace-go.v1/profiler"

	"github.com/MoeGolibrary/go-lib/zlog"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/wire"
)

func main() {
	flag.Parse()

	zlog.InitLogger(zlog.NewConfig(zlog.WithLevel(zapcore.DebugLevel)))
	config.Init()

	server := wire.NewServer()
	server.Start()
	zlog.Default().Info("server started")

	// Datadog Profiler.
	startProfiler()

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	for sig := range signalChan {
		zlog.Default().Info(
			"received signal",
			zap.String("signal", sig.String()),
		)

		server.Stop()
		zlog.Default().Info("server stopped")
		profiler.Stop()

		break
	}
}

func startProfiler() {
	// Datadog Profiler
	if err := profiler.Start(
		profiler.WithProfileTypes(profiler.CPUProfile, profiler.HeapProfile),
	); err != nil {
		zlog.Default().Error("start DD continue profiler failed", zap.Error(err))
	}
}
