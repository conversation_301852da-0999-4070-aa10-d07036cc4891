// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	mock "github.com/stretchr/testify/mock"
	money "google.golang.org/genproto/googleapis/type/money"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
)

// createOrderSource is an autogenerated mock type for the createOrderSource type
type createOrderSource struct {
	mock.Mock
}

type createOrderSource_Expecter struct {
	mock *mock.Mock
}

func (_m *createOrderSource) EXPECT() *createOrderSource_Expecter {
	return &createOrderSource_Expecter{mock: &_m.Mock}
}

// GetAppliedPromotions provides a mock function with no fields
func (_m *createOrderSource) GetAppliedPromotions() *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAppliedPromotions")
	}

	var r0 *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions
	if rf, ok := ret.Get(0).(func() *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions)
		}
	}

	return r0
}

// createOrderSource_GetAppliedPromotions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAppliedPromotions'
type createOrderSource_GetAppliedPromotions_Call struct {
	*mock.Call
}

// GetAppliedPromotions is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetAppliedPromotions() *createOrderSource_GetAppliedPromotions_Call {
	return &createOrderSource_GetAppliedPromotions_Call{Call: _e.mock.On("GetAppliedPromotions")}
}

func (_c *createOrderSource_GetAppliedPromotions_Call) Run(run func()) *createOrderSource_GetAppliedPromotions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetAppliedPromotions_Call) Return(_a0 *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions) *createOrderSource_GetAppliedPromotions_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetAppliedPromotions_Call) RunAndReturn(run func() *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions) *createOrderSource_GetAppliedPromotions_Call {
	_c.Call.Return(run)
	return _c
}

// GetBusinessId provides a mock function with no fields
func (_m *createOrderSource) GetBusinessId() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetBusinessId")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// createOrderSource_GetBusinessId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBusinessId'
type createOrderSource_GetBusinessId_Call struct {
	*mock.Call
}

// GetBusinessId is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetBusinessId() *createOrderSource_GetBusinessId_Call {
	return &createOrderSource_GetBusinessId_Call{Call: _e.mock.On("GetBusinessId")}
}

func (_c *createOrderSource_GetBusinessId_Call) Run(run func()) *createOrderSource_GetBusinessId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetBusinessId_Call) Return(_a0 int64) *createOrderSource_GetBusinessId_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetBusinessId_Call) RunAndReturn(run func() int64) *createOrderSource_GetBusinessId_Call {
	_c.Call.Return(run)
	return _c
}

// GetCompanyId provides a mock function with no fields
func (_m *createOrderSource) GetCompanyId() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetCompanyId")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// createOrderSource_GetCompanyId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCompanyId'
type createOrderSource_GetCompanyId_Call struct {
	*mock.Call
}

// GetCompanyId is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetCompanyId() *createOrderSource_GetCompanyId_Call {
	return &createOrderSource_GetCompanyId_Call{Call: _e.mock.On("GetCompanyId")}
}

func (_c *createOrderSource_GetCompanyId_Call) Run(run func()) *createOrderSource_GetCompanyId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetCompanyId_Call) Return(_a0 int64) *createOrderSource_GetCompanyId_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetCompanyId_Call) RunAndReturn(run func() int64) *createOrderSource_GetCompanyId_Call {
	_c.Call.Return(run)
	return _c
}

// GetCustomerId provides a mock function with no fields
func (_m *createOrderSource) GetCustomerId() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerId")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// createOrderSource_GetCustomerId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCustomerId'
type createOrderSource_GetCustomerId_Call struct {
	*mock.Call
}

// GetCustomerId is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetCustomerId() *createOrderSource_GetCustomerId_Call {
	return &createOrderSource_GetCustomerId_Call{Call: _e.mock.On("GetCustomerId")}
}

func (_c *createOrderSource_GetCustomerId_Call) Run(run func()) *createOrderSource_GetCustomerId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetCustomerId_Call) Return(_a0 int64) *createOrderSource_GetCustomerId_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetCustomerId_Call) RunAndReturn(run func() int64) *createOrderSource_GetCustomerId_Call {
	_c.Call.Return(run)
	return _c
}

// GetItems provides a mock function with no fields
func (_m *createOrderSource) GetItems() []*ordersvcpb.PreviewCreateOrderRequest_CartItem {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetItems")
	}

	var r0 []*ordersvcpb.PreviewCreateOrderRequest_CartItem
	if rf, ok := ret.Get(0).(func() []*ordersvcpb.PreviewCreateOrderRequest_CartItem); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ordersvcpb.PreviewCreateOrderRequest_CartItem)
		}
	}

	return r0
}

// createOrderSource_GetItems_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetItems'
type createOrderSource_GetItems_Call struct {
	*mock.Call
}

// GetItems is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetItems() *createOrderSource_GetItems_Call {
	return &createOrderSource_GetItems_Call{Call: _e.mock.On("GetItems")}
}

func (_c *createOrderSource_GetItems_Call) Run(run func()) *createOrderSource_GetItems_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetItems_Call) Return(_a0 []*ordersvcpb.PreviewCreateOrderRequest_CartItem) *createOrderSource_GetItems_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetItems_Call) RunAndReturn(run func() []*ordersvcpb.PreviewCreateOrderRequest_CartItem) *createOrderSource_GetItems_Call {
	_c.Call.Return(run)
	return _c
}

// GetSourceId provides a mock function with no fields
func (_m *createOrderSource) GetSourceId() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSourceId")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// createOrderSource_GetSourceId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSourceId'
type createOrderSource_GetSourceId_Call struct {
	*mock.Call
}

// GetSourceId is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetSourceId() *createOrderSource_GetSourceId_Call {
	return &createOrderSource_GetSourceId_Call{Call: _e.mock.On("GetSourceId")}
}

func (_c *createOrderSource_GetSourceId_Call) Run(run func()) *createOrderSource_GetSourceId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetSourceId_Call) Return(_a0 int64) *createOrderSource_GetSourceId_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetSourceId_Call) RunAndReturn(run func() int64) *createOrderSource_GetSourceId_Call {
	_c.Call.Return(run)
	return _c
}

// GetSourceType provides a mock function with no fields
func (_m *createOrderSource) GetSourceType() orderpb.OrderSourceType {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSourceType")
	}

	var r0 orderpb.OrderSourceType
	if rf, ok := ret.Get(0).(func() orderpb.OrderSourceType); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(orderpb.OrderSourceType)
	}

	return r0
}

// createOrderSource_GetSourceType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSourceType'
type createOrderSource_GetSourceType_Call struct {
	*mock.Call
}

// GetSourceType is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetSourceType() *createOrderSource_GetSourceType_Call {
	return &createOrderSource_GetSourceType_Call{Call: _e.mock.On("GetSourceType")}
}

func (_c *createOrderSource_GetSourceType_Call) Run(run func()) *createOrderSource_GetSourceType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetSourceType_Call) Return(_a0 orderpb.OrderSourceType) *createOrderSource_GetSourceType_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetSourceType_Call) RunAndReturn(run func() orderpb.OrderSourceType) *createOrderSource_GetSourceType_Call {
	_c.Call.Return(run)
	return _c
}

// GetTipsAmount provides a mock function with no fields
func (_m *createOrderSource) GetTipsAmount() *money.Money {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTipsAmount")
	}

	var r0 *money.Money
	if rf, ok := ret.Get(0).(func() *money.Money); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*money.Money)
		}
	}

	return r0
}

// createOrderSource_GetTipsAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTipsAmount'
type createOrderSource_GetTipsAmount_Call struct {
	*mock.Call
}

// GetTipsAmount is a helper method to define mock.On call
func (_e *createOrderSource_Expecter) GetTipsAmount() *createOrderSource_GetTipsAmount_Call {
	return &createOrderSource_GetTipsAmount_Call{Call: _e.mock.On("GetTipsAmount")}
}

func (_c *createOrderSource_GetTipsAmount_Call) Run(run func()) *createOrderSource_GetTipsAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *createOrderSource_GetTipsAmount_Call) Return(_a0 *money.Money) *createOrderSource_GetTipsAmount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *createOrderSource_GetTipsAmount_Call) RunAndReturn(run func() *money.Money) *createOrderSource_GetTipsAmount_Call {
	_c.Call.Return(run)
	return _c
}

// newCreateOrderSource creates a new instance of createOrderSource. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newCreateOrderSource(t interface {
	mock.TestingT
	Cleanup(func())
}) *createOrderSource {
	mock := &createOrderSource{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
