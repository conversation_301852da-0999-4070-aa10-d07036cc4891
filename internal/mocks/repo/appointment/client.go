// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	appointmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"

	mock "github.com/stretchr/testify/mock"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// ListPetDetailByGroomingID provides a mock function with given fields: ctx, groomingID
func (_m *Client) ListPetDetailByGroomingID(ctx context.Context, groomingID int64) (*appointmentsvcpb.GetPetDetailListResponse, error) {
	ret := _m.Called(ctx, groomingID)

	if len(ret) == 0 {
		panic("no return value specified for ListPetDetailByGroomingID")
	}

	var r0 *appointmentsvcpb.GetPetDetailListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*appointmentsvcpb.GetPetDetailListResponse, error)); ok {
		return rf(ctx, groomingID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *appointmentsvcpb.GetPetDetailListResponse); ok {
		r0 = rf(ctx, groomingID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appointmentsvcpb.GetPetDetailListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, groomingID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_ListPetDetailByGroomingID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPetDetailByGroomingID'
type Client_ListPetDetailByGroomingID_Call struct {
	*mock.Call
}

// ListPetDetailByGroomingID is a helper method to define mock.On call
//   - ctx context.Context
//   - groomingID int64
func (_e *Client_Expecter) ListPetDetailByGroomingID(ctx interface{}, groomingID interface{}) *Client_ListPetDetailByGroomingID_Call {
	return &Client_ListPetDetailByGroomingID_Call{Call: _e.mock.On("ListPetDetailByGroomingID", ctx, groomingID)}
}

func (_c *Client_ListPetDetailByGroomingID_Call) Run(run func(ctx context.Context, groomingID int64)) *Client_ListPetDetailByGroomingID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Client_ListPetDetailByGroomingID_Call) Return(_a0 *appointmentsvcpb.GetPetDetailListResponse, _a1 error) *Client_ListPetDetailByGroomingID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_ListPetDetailByGroomingID_Call) RunAndReturn(run func(context.Context, int64) (*appointmentsvcpb.GetPetDetailListResponse, error)) *Client_ListPetDetailByGroomingID_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
