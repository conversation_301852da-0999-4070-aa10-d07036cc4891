// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	business "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/business"

	mock "github.com/stretchr/testify/mock"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// GetPayrollSetting provides a mock function with given fields: ctx, bid
func (_m *Client) GetPayrollSetting(ctx context.Context, bid int64) (*business.PayrollSetting, error) {
	ret := _m.Called(ctx, bid)

	if len(ret) == 0 {
		panic("no return value specified for GetPayrollSetting")
	}

	var r0 *business.PayrollSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*business.PayrollSetting, error)); ok {
		return rf(ctx, bid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *business.PayrollSetting); ok {
		r0 = rf(ctx, bid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*business.PayrollSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, bid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_GetPayrollSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPayrollSetting'
type Client_GetPayrollSetting_Call struct {
	*mock.Call
}

// GetPayrollSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - bid int64
func (_e *Client_Expecter) GetPayrollSetting(ctx interface{}, bid interface{}) *Client_GetPayrollSetting_Call {
	return &Client_GetPayrollSetting_Call{Call: _e.mock.On("GetPayrollSetting", ctx, bid)}
}

func (_c *Client_GetPayrollSetting_Call) Run(run func(ctx context.Context, bid int64)) *Client_GetPayrollSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Client_GetPayrollSetting_Call) Return(_a0 *business.PayrollSetting, _a1 error) *Client_GetPayrollSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_GetPayrollSetting_Call) RunAndReturn(run func(context.Context, int64) (*business.PayrollSetting, error)) *Client_GetPayrollSetting_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
