// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// DepositChangeLogRepo is an autogenerated mock type for the DepositChangeLogRepo type
type DepositChangeLogRepo struct {
	mock.Mock
}

type DepositChangeLogRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *DepositChangeLogRepo) EXPECT() *DepositChangeLogRepo_Expecter {
	return &DepositChangeLogRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, depositChangeLog
func (_m *DepositChangeLogRepo) Create(ctx context.Context, depositChangeLog *model.DepositChangeLog) error {
	ret := _m.Called(ctx, depositChangeLog)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DepositChangeLog) error); ok {
		r0 = rf(ctx, depositChangeLog)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DepositChangeLogRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type DepositChangeLogRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - depositChangeLog *model.DepositChangeLog
func (_e *DepositChangeLogRepo_Expecter) Create(ctx interface{}, depositChangeLog interface{}) *DepositChangeLogRepo_Create_Call {
	return &DepositChangeLogRepo_Create_Call{Call: _e.mock.On("Create", ctx, depositChangeLog)}
}

func (_c *DepositChangeLogRepo_Create_Call) Run(run func(ctx context.Context, depositChangeLog *model.DepositChangeLog)) *DepositChangeLogRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DepositChangeLog))
	})
	return _c
}

func (_c *DepositChangeLogRepo_Create_Call) Return(_a0 error) *DepositChangeLogRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DepositChangeLogRepo_Create_Call) RunAndReturn(run func(context.Context, *model.DepositChangeLog) error) *DepositChangeLogRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetDeductionByDestOrderID provides a mock function with given fields: ctx, destOrderID
func (_m *DepositChangeLogRepo) GetDeductionByDestOrderID(ctx context.Context, destOrderID int64) (*model.DepositChangeLog, error) {
	ret := _m.Called(ctx, destOrderID)

	if len(ret) == 0 {
		panic("no return value specified for GetDeductionByDestOrderID")
	}

	var r0 *model.DepositChangeLog
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.DepositChangeLog, error)); ok {
		return rf(ctx, destOrderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.DepositChangeLog); ok {
		r0 = rf(ctx, destOrderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositChangeLog)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, destOrderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositChangeLogRepo_GetDeductionByDestOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeductionByDestOrderID'
type DepositChangeLogRepo_GetDeductionByDestOrderID_Call struct {
	*mock.Call
}

// GetDeductionByDestOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - destOrderID int64
func (_e *DepositChangeLogRepo_Expecter) GetDeductionByDestOrderID(ctx interface{}, destOrderID interface{}) *DepositChangeLogRepo_GetDeductionByDestOrderID_Call {
	return &DepositChangeLogRepo_GetDeductionByDestOrderID_Call{Call: _e.mock.On("GetDeductionByDestOrderID", ctx, destOrderID)}
}

func (_c *DepositChangeLogRepo_GetDeductionByDestOrderID_Call) Run(run func(ctx context.Context, destOrderID int64)) *DepositChangeLogRepo_GetDeductionByDestOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositChangeLogRepo_GetDeductionByDestOrderID_Call) Return(_a0 *model.DepositChangeLog, _a1 error) *DepositChangeLogRepo_GetDeductionByDestOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositChangeLogRepo_GetDeductionByDestOrderID_Call) RunAndReturn(run func(context.Context, int64) (*model.DepositChangeLog, error)) *DepositChangeLogRepo_GetDeductionByDestOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestByDepositOrderID provides a mock function with given fields: ctx, depositOrderID
func (_m *DepositChangeLogRepo) GetLatestByDepositOrderID(ctx context.Context, depositOrderID int64) (*model.DepositChangeLog, error) {
	ret := _m.Called(ctx, depositOrderID)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestByDepositOrderID")
	}

	var r0 *model.DepositChangeLog
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.DepositChangeLog, error)); ok {
		return rf(ctx, depositOrderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.DepositChangeLog); ok {
		r0 = rf(ctx, depositOrderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositChangeLog)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, depositOrderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositChangeLogRepo_GetLatestByDepositOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestByDepositOrderID'
type DepositChangeLogRepo_GetLatestByDepositOrderID_Call struct {
	*mock.Call
}

// GetLatestByDepositOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - depositOrderID int64
func (_e *DepositChangeLogRepo_Expecter) GetLatestByDepositOrderID(ctx interface{}, depositOrderID interface{}) *DepositChangeLogRepo_GetLatestByDepositOrderID_Call {
	return &DepositChangeLogRepo_GetLatestByDepositOrderID_Call{Call: _e.mock.On("GetLatestByDepositOrderID", ctx, depositOrderID)}
}

func (_c *DepositChangeLogRepo_GetLatestByDepositOrderID_Call) Run(run func(ctx context.Context, depositOrderID int64)) *DepositChangeLogRepo_GetLatestByDepositOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositChangeLogRepo_GetLatestByDepositOrderID_Call) Return(_a0 *model.DepositChangeLog, _a1 error) *DepositChangeLogRepo_GetLatestByDepositOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositChangeLogRepo_GetLatestByDepositOrderID_Call) RunAndReturn(run func(context.Context, int64) (*model.DepositChangeLog, error)) *DepositChangeLogRepo_GetLatestByDepositOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListByDepositOrderID provides a mock function with given fields: ctx, depositOrderID
func (_m *DepositChangeLogRepo) ListByDepositOrderID(ctx context.Context, depositOrderID int64) ([]*model.DepositChangeLog, error) {
	ret := _m.Called(ctx, depositOrderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByDepositOrderID")
	}

	var r0 []*model.DepositChangeLog
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.DepositChangeLog, error)); ok {
		return rf(ctx, depositOrderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.DepositChangeLog); ok {
		r0 = rf(ctx, depositOrderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DepositChangeLog)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, depositOrderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositChangeLogRepo_ListByDepositOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByDepositOrderID'
type DepositChangeLogRepo_ListByDepositOrderID_Call struct {
	*mock.Call
}

// ListByDepositOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - depositOrderID int64
func (_e *DepositChangeLogRepo_Expecter) ListByDepositOrderID(ctx interface{}, depositOrderID interface{}) *DepositChangeLogRepo_ListByDepositOrderID_Call {
	return &DepositChangeLogRepo_ListByDepositOrderID_Call{Call: _e.mock.On("ListByDepositOrderID", ctx, depositOrderID)}
}

func (_c *DepositChangeLogRepo_ListByDepositOrderID_Call) Run(run func(ctx context.Context, depositOrderID int64)) *DepositChangeLogRepo_ListByDepositOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositChangeLogRepo_ListByDepositOrderID_Call) Return(_a0 []*model.DepositChangeLog, _a1 error) *DepositChangeLogRepo_ListByDepositOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositChangeLogRepo_ListByDepositOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.DepositChangeLog, error)) *DepositChangeLogRepo_ListByDepositOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// NewDepositChangeLogRepo creates a new instance of DepositChangeLogRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDepositChangeLogRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *DepositChangeLogRepo {
	mock := &DepositChangeLogRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
