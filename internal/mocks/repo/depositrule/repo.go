// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// Repo is an autogenerated mock type for the Repo type
type Repo struct {
	mock.Mock
}

type Repo_Expecter struct {
	mock *mock.Mock
}

func (_m *Repo) EXPECT() *Repo_Expecter {
	return &Repo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, depositRules
func (_m *Repo) Create(ctx context.Context, depositRules []*model.DepositRule) error {
	ret := _m.Called(ctx, depositRules)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.DepositRule) error); ok {
		r0 = rf(ctx, depositRules)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type Repo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - depositRules []*model.DepositRule
func (_e *Repo_Expecter) Create(ctx interface{}, depositRules interface{}) *Repo_Create_Call {
	return &Repo_Create_Call{Call: _e.mock.On("Create", ctx, depositRules)}
}

func (_c *Repo_Create_Call) Run(run func(ctx context.Context, depositRules []*model.DepositRule)) *Repo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.DepositRule))
	})
	return _c
}

func (_c *Repo_Create_Call) Return(_a0 error) *Repo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repo_Create_Call) RunAndReturn(run func(context.Context, []*model.DepositRule) error) *Repo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, ruleID
func (_m *Repo) Delete(ctx context.Context, ruleID int64) error {
	ret := _m.Called(ctx, ruleID)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, ruleID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repo_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type Repo_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID int64
func (_e *Repo_Expecter) Delete(ctx interface{}, ruleID interface{}) *Repo_Delete_Call {
	return &Repo_Delete_Call{Call: _e.mock.On("Delete", ctx, ruleID)}
}

func (_c *Repo_Delete_Call) Run(run func(ctx context.Context, ruleID int64)) *Repo_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Repo_Delete_Call) Return(_a0 error) *Repo_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repo_Delete_Call) RunAndReturn(run func(context.Context, int64) error) *Repo_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, ruleID
func (_m *Repo) Get(ctx context.Context, ruleID int64) (*model.DepositRule, error) {
	ret := _m.Called(ctx, ruleID)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.DepositRule, error)); ok {
		return rf(ctx, ruleID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.DepositRule); ok {
		r0 = rf(ctx, ruleID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, ruleID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type Repo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID int64
func (_e *Repo_Expecter) Get(ctx interface{}, ruleID interface{}) *Repo_Get_Call {
	return &Repo_Get_Call{Call: _e.mock.On("Get", ctx, ruleID)}
}

func (_c *Repo_Get_Call) Run(run func(ctx context.Context, ruleID int64)) *Repo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Repo_Get_Call) Return(_a0 *model.DepositRule, _a1 error) *Repo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repo_Get_Call) RunAndReturn(run func(context.Context, int64) (*model.DepositRule, error)) *Repo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// ListByBusinessID provides a mock function with given fields: ctx, businessID
func (_m *Repo) ListByBusinessID(ctx context.Context, businessID int64) ([]*model.DepositRule, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for ListByBusinessID")
	}

	var r0 []*model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.DepositRule, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.DepositRule); ok {
		r0 = rf(ctx, businessID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repo_ListByBusinessID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByBusinessID'
type Repo_ListByBusinessID_Call struct {
	*mock.Call
}

// ListByBusinessID is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
func (_e *Repo_Expecter) ListByBusinessID(ctx interface{}, businessID interface{}) *Repo_ListByBusinessID_Call {
	return &Repo_ListByBusinessID_Call{Call: _e.mock.On("ListByBusinessID", ctx, businessID)}
}

func (_c *Repo_ListByBusinessID_Call) Run(run func(ctx context.Context, businessID int64)) *Repo_ListByBusinessID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Repo_ListByBusinessID_Call) Return(_a0 []*model.DepositRule, _a1 error) *Repo_ListByBusinessID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repo_ListByBusinessID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.DepositRule, error)) *Repo_ListByBusinessID_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: ctx, depositRule
func (_m *Repo) Update(ctx context.Context, depositRule *model.DepositRule) error {
	ret := _m.Called(ctx, depositRule)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DepositRule) error); ok {
		r0 = rf(ctx, depositRule)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repo_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type Repo_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - depositRule *model.DepositRule
func (_e *Repo_Expecter) Update(ctx interface{}, depositRule interface{}) *Repo_Update_Call {
	return &Repo_Update_Call{Call: _e.mock.On("Update", ctx, depositRule)}
}

func (_c *Repo_Update_Call) Run(run func(ctx context.Context, depositRule *model.DepositRule)) *Repo_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.DepositRule))
	})
	return _c
}

func (_c *Repo_Update_Call) Return(_a0 error) *Repo_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repo_Update_Call) RunAndReturn(run func(context.Context, *model.DepositRule) error) *Repo_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewRepo creates a new instance of Repo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repo {
	mock := &Repo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
