// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// LegacyOrderTipsSplitDetailRepo is an autogenerated mock type for the LegacyOrderTipsSplitDetailRepo type
type LegacyOrderTipsSplitDetailRepo struct {
	mock.Mock
}

type LegacyOrderTipsSplitDetailRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *LegacyOrderTipsSplitDetailRepo) EXPECT() *LegacyOrderTipsSplitDetailRepo_Expecter {
	return &LegacyOrderTipsSplitDetailRepo_Expecter{mock: &_m.Mock}
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *LegacyOrderTipsSplitDetailRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderTipsSplitDetail, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.OrderTipsSplitDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderTipsSplitDetail, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderTipsSplitDetail); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderTipsSplitDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *LegacyOrderTipsSplitDetailRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call {
	return &LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call) Return(_a0 []*model.OrderTipsSplitDetail, _a1 error) *LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderTipsSplitDetail, error)) *LegacyOrderTipsSplitDetailRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderIDs provides a mock function with given fields: ctx, orderIDs
func (_m *LegacyOrderTipsSplitDetailRepo) ListByOrderIDs(ctx context.Context, orderIDs []int64) ([]*model.OrderTipsSplitDetail, error) {
	ret := _m.Called(ctx, orderIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderIDs")
	}

	var r0 []*model.OrderTipsSplitDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.OrderTipsSplitDetail, error)); ok {
		return rf(ctx, orderIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.OrderTipsSplitDetail); ok {
		r0 = rf(ctx, orderIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderTipsSplitDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, orderIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderIDs'
type LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call struct {
	*mock.Call
}

// ListByOrderIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - orderIDs []int64
func (_e *LegacyOrderTipsSplitDetailRepo_Expecter) ListByOrderIDs(ctx interface{}, orderIDs interface{}) *LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call {
	return &LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call{Call: _e.mock.On("ListByOrderIDs", ctx, orderIDs)}
}

func (_c *LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call) Run(run func(ctx context.Context, orderIDs []int64)) *LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call) Return(_a0 []*model.OrderTipsSplitDetail, _a1 error) *LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.OrderTipsSplitDetail, error)) *LegacyOrderTipsSplitDetailRepo_ListByOrderIDs_Call {
	_c.Call.Return(run)
	return _c
}

// NewLegacyOrderTipsSplitDetailRepo creates a new instance of LegacyOrderTipsSplitDetailRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLegacyOrderTipsSplitDetailRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *LegacyOrderTipsSplitDetailRepo {
	mock := &LegacyOrderTipsSplitDetailRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
