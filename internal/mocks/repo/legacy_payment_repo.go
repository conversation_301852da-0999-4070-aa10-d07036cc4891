// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// LegacyPaymentRepo is an autogenerated mock type for the LegacyPaymentRepo type
type LegacyPaymentRepo struct {
	mock.Mock
}

type LegacyPaymentRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *LegacyPaymentRepo) EXPECT() *LegacyPaymentRepo_Expecter {
	return &LegacyPaymentRepo_Expecter{mock: &_m.Mock}
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *LegacyPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderPayment, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderPayment); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyPaymentRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type LegacyPaymentRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *LegacyPaymentRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *LegacyPaymentRepo_ListByOrderID_Call {
	return &LegacyPaymentRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *LegacyPaymentRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *LegacyPaymentRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *LegacyPaymentRepo_ListByOrderID_Call) Return(_a0 []*model.OrderPayment, _a1 error) *LegacyPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyPaymentRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderPayment, error)) *LegacyPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// NewLegacyPaymentRepo creates a new instance of LegacyPaymentRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLegacyPaymentRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *LegacyPaymentRepo {
	mock := &LegacyPaymentRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
