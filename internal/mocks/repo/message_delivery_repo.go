// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// MessageDeliveryRepo is an autogenerated mock type for the MessageDeliveryRepo type
type MessageDeliveryRepo struct {
	mock.Mock
}

type MessageDeliveryRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *MessageDeliveryRepo) EXPECT() *MessageDeliveryRepo_Expecter {
	return &MessageDeliveryRepo_Expecter{mock: &_m.Mock}
}

// CreateCanceled provides a mock function with given fields: ctx, od
func (_m *MessageDeliveryRepo) CreateCanceled(ctx context.Context, od *model.Order) error {
	ret := _m.Called(ctx, od)

	if len(ret) == 0 {
		panic("no return value specified for CreateCanceled")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) error); ok {
		r0 = rf(ctx, od)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MessageDeliveryRepo_CreateCanceled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateCanceled'
type MessageDeliveryRepo_CreateCanceled_Call struct {
	*mock.Call
}

// CreateCanceled is a helper method to define mock.On call
//   - ctx context.Context
//   - od *model.Order
func (_e *MessageDeliveryRepo_Expecter) CreateCanceled(ctx interface{}, od interface{}) *MessageDeliveryRepo_CreateCanceled_Call {
	return &MessageDeliveryRepo_CreateCanceled_Call{Call: _e.mock.On("CreateCanceled", ctx, od)}
}

func (_c *MessageDeliveryRepo_CreateCanceled_Call) Run(run func(ctx context.Context, od *model.Order)) *MessageDeliveryRepo_CreateCanceled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *MessageDeliveryRepo_CreateCanceled_Call) Return(_a0 error) *MessageDeliveryRepo_CreateCanceled_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MessageDeliveryRepo_CreateCanceled_Call) RunAndReturn(run func(context.Context, *model.Order) error) *MessageDeliveryRepo_CreateCanceled_Call {
	_c.Call.Return(run)
	return _c
}

// CreateCompleted provides a mock function with given fields: ctx, od
func (_m *MessageDeliveryRepo) CreateCompleted(ctx context.Context, od *model.Order) error {
	ret := _m.Called(ctx, od)

	if len(ret) == 0 {
		panic("no return value specified for CreateCompleted")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) error); ok {
		r0 = rf(ctx, od)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MessageDeliveryRepo_CreateCompleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateCompleted'
type MessageDeliveryRepo_CreateCompleted_Call struct {
	*mock.Call
}

// CreateCompleted is a helper method to define mock.On call
//   - ctx context.Context
//   - od *model.Order
func (_e *MessageDeliveryRepo_Expecter) CreateCompleted(ctx interface{}, od interface{}) *MessageDeliveryRepo_CreateCompleted_Call {
	return &MessageDeliveryRepo_CreateCompleted_Call{Call: _e.mock.On("CreateCompleted", ctx, od)}
}

func (_c *MessageDeliveryRepo_CreateCompleted_Call) Run(run func(ctx context.Context, od *model.Order)) *MessageDeliveryRepo_CreateCompleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *MessageDeliveryRepo_CreateCompleted_Call) Return(_a0 error) *MessageDeliveryRepo_CreateCompleted_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MessageDeliveryRepo_CreateCompleted_Call) RunAndReturn(run func(context.Context, *model.Order) error) *MessageDeliveryRepo_CreateCompleted_Call {
	_c.Call.Return(run)
	return _c
}

// CreateCreated provides a mock function with given fields: ctx, od
func (_m *MessageDeliveryRepo) CreateCreated(ctx context.Context, od *model.Order) error {
	ret := _m.Called(ctx, od)

	if len(ret) == 0 {
		panic("no return value specified for CreateCreated")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) error); ok {
		r0 = rf(ctx, od)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MessageDeliveryRepo_CreateCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateCreated'
type MessageDeliveryRepo_CreateCreated_Call struct {
	*mock.Call
}

// CreateCreated is a helper method to define mock.On call
//   - ctx context.Context
//   - od *model.Order
func (_e *MessageDeliveryRepo_Expecter) CreateCreated(ctx interface{}, od interface{}) *MessageDeliveryRepo_CreateCreated_Call {
	return &MessageDeliveryRepo_CreateCreated_Call{Call: _e.mock.On("CreateCreated", ctx, od)}
}

func (_c *MessageDeliveryRepo_CreateCreated_Call) Run(run func(ctx context.Context, od *model.Order)) *MessageDeliveryRepo_CreateCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *MessageDeliveryRepo_CreateCreated_Call) Return(_a0 error) *MessageDeliveryRepo_CreateCreated_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MessageDeliveryRepo_CreateCreated_Call) RunAndReturn(run func(context.Context, *model.Order) error) *MessageDeliveryRepo_CreateCreated_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRefunded provides a mock function with given fields: ctx, rod
func (_m *MessageDeliveryRepo) CreateRefunded(ctx context.Context, rod *model.RefundOrder) error {
	ret := _m.Called(ctx, rod)

	if len(ret) == 0 {
		panic("no return value specified for CreateRefunded")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrder) error); ok {
		r0 = rf(ctx, rod)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MessageDeliveryRepo_CreateRefunded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRefunded'
type MessageDeliveryRepo_CreateRefunded_Call struct {
	*mock.Call
}

// CreateRefunded is a helper method to define mock.On call
//   - ctx context.Context
//   - rod *model.RefundOrder
func (_e *MessageDeliveryRepo_Expecter) CreateRefunded(ctx interface{}, rod interface{}) *MessageDeliveryRepo_CreateRefunded_Call {
	return &MessageDeliveryRepo_CreateRefunded_Call{Call: _e.mock.On("CreateRefunded", ctx, rod)}
}

func (_c *MessageDeliveryRepo_CreateRefunded_Call) Run(run func(ctx context.Context, rod *model.RefundOrder)) *MessageDeliveryRepo_CreateRefunded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrder))
	})
	return _c
}

func (_c *MessageDeliveryRepo_CreateRefunded_Call) Return(_a0 error) *MessageDeliveryRepo_CreateRefunded_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MessageDeliveryRepo_CreateRefunded_Call) RunAndReturn(run func(context.Context, *model.RefundOrder) error) *MessageDeliveryRepo_CreateRefunded_Call {
	_c.Call.Return(run)
	return _c
}

// NewMessageDeliveryRepo creates a new instance of MessageDeliveryRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMessageDeliveryRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MessageDeliveryRepo {
	mock := &MessageDeliveryRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
