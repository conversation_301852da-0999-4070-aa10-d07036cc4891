// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// OrderItemAssignedAmountRepo is an autogenerated mock type for the OrderItemAssignedAmountRepo type
type OrderItemAssignedAmountRepo struct {
	mock.Mock
}

type OrderItemAssignedAmountRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderItemAssignedAmountRepo) EXPECT() *OrderItemAssignedAmountRepo_Expecter {
	return &OrderItemAssignedAmountRepo_Expecter{mock: &_m.Mock}
}

// BatchGetByOrderID provides a mock function with given fields: ctx, orderID
func (_m *OrderItemAssignedAmountRepo) BatchGetByOrderID(ctx context.Context, orderID int64) ([]*model.OrderItemAssignedAmount, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetByOrderID")
	}

	var r0 []*model.OrderItemAssignedAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderItemAssignedAmount, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderItemAssignedAmount); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderItemAssignedAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderItemAssignedAmountRepo_BatchGetByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGetByOrderID'
type OrderItemAssignedAmountRepo_BatchGetByOrderID_Call struct {
	*mock.Call
}

// BatchGetByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderItemAssignedAmountRepo_Expecter) BatchGetByOrderID(ctx interface{}, orderID interface{}) *OrderItemAssignedAmountRepo_BatchGetByOrderID_Call {
	return &OrderItemAssignedAmountRepo_BatchGetByOrderID_Call{Call: _e.mock.On("BatchGetByOrderID", ctx, orderID)}
}

func (_c *OrderItemAssignedAmountRepo_BatchGetByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *OrderItemAssignedAmountRepo_BatchGetByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderItemAssignedAmountRepo_BatchGetByOrderID_Call) Return(_a0 []*model.OrderItemAssignedAmount, _a1 error) *OrderItemAssignedAmountRepo_BatchGetByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderItemAssignedAmountRepo_BatchGetByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderItemAssignedAmount, error)) *OrderItemAssignedAmountRepo_BatchGetByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// BatchInsert provides a mock function with given fields: ctx, amounts
func (_m *OrderItemAssignedAmountRepo) BatchInsert(ctx context.Context, amounts []*model.OrderItemAssignedAmount) error {
	ret := _m.Called(ctx, amounts)

	if len(ret) == 0 {
		panic("no return value specified for BatchInsert")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.OrderItemAssignedAmount) error); ok {
		r0 = rf(ctx, amounts)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderItemAssignedAmountRepo_BatchInsert_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchInsert'
type OrderItemAssignedAmountRepo_BatchInsert_Call struct {
	*mock.Call
}

// BatchInsert is a helper method to define mock.On call
//   - ctx context.Context
//   - amounts []*model.OrderItemAssignedAmount
func (_e *OrderItemAssignedAmountRepo_Expecter) BatchInsert(ctx interface{}, amounts interface{}) *OrderItemAssignedAmountRepo_BatchInsert_Call {
	return &OrderItemAssignedAmountRepo_BatchInsert_Call{Call: _e.mock.On("BatchInsert", ctx, amounts)}
}

func (_c *OrderItemAssignedAmountRepo_BatchInsert_Call) Run(run func(ctx context.Context, amounts []*model.OrderItemAssignedAmount)) *OrderItemAssignedAmountRepo_BatchInsert_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.OrderItemAssignedAmount))
	})
	return _c
}

func (_c *OrderItemAssignedAmountRepo_BatchInsert_Call) Return(_a0 error) *OrderItemAssignedAmountRepo_BatchInsert_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderItemAssignedAmountRepo_BatchInsert_Call) RunAndReturn(run func(context.Context, []*model.OrderItemAssignedAmount) error) *OrderItemAssignedAmountRepo_BatchInsert_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderItemAssignedAmountRepo creates a new instance of OrderItemAssignedAmountRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderItemAssignedAmountRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderItemAssignedAmountRepo {
	mock := &OrderItemAssignedAmountRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
