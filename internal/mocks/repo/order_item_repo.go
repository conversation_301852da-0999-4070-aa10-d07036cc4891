// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// OrderItemRepo is an autogenerated mock type for the OrderItemRepo type
type OrderItemRepo struct {
	mock.Mock
}

type OrderItemRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderItemRepo) EXPECT() *OrderItemRepo_Expecter {
	return &OrderItemRepo_Expecter{mock: &_m.Mock}
}

// BatchCreate provides a mock function with given fields: ctx, items
func (_m *OrderItemRepo) BatchCreate(ctx context.Context, items []*model.OrderItem) error {
	ret := _m.Called(ctx, items)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.OrderItem) error); ok {
		r0 = rf(ctx, items)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderItemRepo_BatchCreate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCreate'
type OrderItemRepo_BatchCreate_Call struct {
	*mock.Call
}

// BatchCreate is a helper method to define mock.On call
//   - ctx context.Context
//   - items []*model.OrderItem
func (_e *OrderItemRepo_Expecter) BatchCreate(ctx interface{}, items interface{}) *OrderItemRepo_BatchCreate_Call {
	return &OrderItemRepo_BatchCreate_Call{Call: _e.mock.On("BatchCreate", ctx, items)}
}

func (_c *OrderItemRepo_BatchCreate_Call) Run(run func(ctx context.Context, items []*model.OrderItem)) *OrderItemRepo_BatchCreate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.OrderItem))
	})
	return _c
}

func (_c *OrderItemRepo_BatchCreate_Call) Return(_a0 error) *OrderItemRepo_BatchCreate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderItemRepo_BatchCreate_Call) RunAndReturn(run func(context.Context, []*model.OrderItem) error) *OrderItemRepo_BatchCreate_Call {
	_c.Call.Return(run)
	return _c
}

// BatchGet provides a mock function with given fields: ctx, ids
func (_m *OrderItemRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.OrderItem, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for BatchGet")
	}

	var r0 []*model.OrderItem
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.OrderItem, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.OrderItem); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderItem)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderItemRepo_BatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGet'
type OrderItemRepo_BatchGet_Call struct {
	*mock.Call
}

// BatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *OrderItemRepo_Expecter) BatchGet(ctx interface{}, ids interface{}) *OrderItemRepo_BatchGet_Call {
	return &OrderItemRepo_BatchGet_Call{Call: _e.mock.On("BatchGet", ctx, ids)}
}

func (_c *OrderItemRepo_BatchGet_Call) Run(run func(ctx context.Context, ids []int64)) *OrderItemRepo_BatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *OrderItemRepo_BatchGet_Call) Return(_a0 []*model.OrderItem, _a1 error) *OrderItemRepo_BatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderItemRepo_BatchGet_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.OrderItem, error)) *OrderItemRepo_BatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, id
func (_m *OrderItemRepo) Get(ctx context.Context, id int64) (*model.OrderItem, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.OrderItem
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.OrderItem, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.OrderItem); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderItem)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderItemRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type OrderItemRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *OrderItemRepo_Expecter) Get(ctx interface{}, id interface{}) *OrderItemRepo_Get_Call {
	return &OrderItemRepo_Get_Call{Call: _e.mock.On("Get", ctx, id)}
}

func (_c *OrderItemRepo_Get_Call) Run(run func(ctx context.Context, id int64)) *OrderItemRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderItemRepo_Get_Call) Return(_a0 *model.OrderItem, _a1 error) *OrderItemRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderItemRepo_Get_Call) RunAndReturn(run func(context.Context, int64) (*model.OrderItem, error)) *OrderItemRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrder provides a mock function with given fields: ctx, orderID
func (_m *OrderItemRepo) ListByOrder(ctx context.Context, orderID int64) ([]*model.OrderItem, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrder")
	}

	var r0 []*model.OrderItem
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderItem, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderItem); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderItem)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderItemRepo_ListByOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrder'
type OrderItemRepo_ListByOrder_Call struct {
	*mock.Call
}

// ListByOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderItemRepo_Expecter) ListByOrder(ctx interface{}, orderID interface{}) *OrderItemRepo_ListByOrder_Call {
	return &OrderItemRepo_ListByOrder_Call{Call: _e.mock.On("ListByOrder", ctx, orderID)}
}

func (_c *OrderItemRepo_ListByOrder_Call) Run(run func(ctx context.Context, orderID int64)) *OrderItemRepo_ListByOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderItemRepo_ListByOrder_Call) Return(_a0 []*model.OrderItem, _a1 error) *OrderItemRepo_ListByOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderItemRepo_ListByOrder_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderItem, error)) *OrderItemRepo_ListByOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ListItemsByOrderIDs provides a mock function with given fields: ctx, orderIDs
func (_m *OrderItemRepo) ListItemsByOrderIDs(ctx context.Context, orderIDs []int64) ([]*model.OrderItem, error) {
	ret := _m.Called(ctx, orderIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListItemsByOrderIDs")
	}

	var r0 []*model.OrderItem
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.OrderItem, error)); ok {
		return rf(ctx, orderIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.OrderItem); ok {
		r0 = rf(ctx, orderIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderItem)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, orderIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderItemRepo_ListItemsByOrderIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListItemsByOrderIDs'
type OrderItemRepo_ListItemsByOrderIDs_Call struct {
	*mock.Call
}

// ListItemsByOrderIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - orderIDs []int64
func (_e *OrderItemRepo_Expecter) ListItemsByOrderIDs(ctx interface{}, orderIDs interface{}) *OrderItemRepo_ListItemsByOrderIDs_Call {
	return &OrderItemRepo_ListItemsByOrderIDs_Call{Call: _e.mock.On("ListItemsByOrderIDs", ctx, orderIDs)}
}

func (_c *OrderItemRepo_ListItemsByOrderIDs_Call) Run(run func(ctx context.Context, orderIDs []int64)) *OrderItemRepo_ListItemsByOrderIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *OrderItemRepo_ListItemsByOrderIDs_Call) Return(_a0 []*model.OrderItem, _a1 error) *OrderItemRepo_ListItemsByOrderIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderItemRepo_ListItemsByOrderIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.OrderItem, error)) *OrderItemRepo_ListItemsByOrderIDs_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRefund provides a mock function with given fields: ctx, refundItem
func (_m *OrderItemRepo) UpdateRefund(ctx context.Context, refundItem *model.RefundOrderItem) (int64, error) {
	ret := _m.Called(ctx, refundItem)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRefund")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderItem) (int64, error)); ok {
		return rf(ctx, refundItem)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderItem) int64); ok {
		r0 = rf(ctx, refundItem)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.RefundOrderItem) error); ok {
		r1 = rf(ctx, refundItem)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderItemRepo_UpdateRefund_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRefund'
type OrderItemRepo_UpdateRefund_Call struct {
	*mock.Call
}

// UpdateRefund is a helper method to define mock.On call
//   - ctx context.Context
//   - refundItem *model.RefundOrderItem
func (_e *OrderItemRepo_Expecter) UpdateRefund(ctx interface{}, refundItem interface{}) *OrderItemRepo_UpdateRefund_Call {
	return &OrderItemRepo_UpdateRefund_Call{Call: _e.mock.On("UpdateRefund", ctx, refundItem)}
}

func (_c *OrderItemRepo_UpdateRefund_Call) Run(run func(ctx context.Context, refundItem *model.RefundOrderItem)) *OrderItemRepo_UpdateRefund_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrderItem))
	})
	return _c
}

func (_c *OrderItemRepo_UpdateRefund_Call) Return(_a0 int64, _a1 error) *OrderItemRepo_UpdateRefund_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderItemRepo_UpdateRefund_Call) RunAndReturn(run func(context.Context, *model.RefundOrderItem) (int64, error)) *OrderItemRepo_UpdateRefund_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStaffID provides a mock function with given fields: ctx, id, staffID
func (_m *OrderItemRepo) UpdateStaffID(ctx context.Context, id int64, staffID int64) (int64, error) {
	ret := _m.Called(ctx, id, staffID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStaffID")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64) (int64, error)); ok {
		return rf(ctx, id, staffID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64) int64); ok {
		r0 = rf(ctx, id, staffID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64) error); ok {
		r1 = rf(ctx, id, staffID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderItemRepo_UpdateStaffID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStaffID'
type OrderItemRepo_UpdateStaffID_Call struct {
	*mock.Call
}

// UpdateStaffID is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
//   - staffID int64
func (_e *OrderItemRepo_Expecter) UpdateStaffID(ctx interface{}, id interface{}, staffID interface{}) *OrderItemRepo_UpdateStaffID_Call {
	return &OrderItemRepo_UpdateStaffID_Call{Call: _e.mock.On("UpdateStaffID", ctx, id, staffID)}
}

func (_c *OrderItemRepo_UpdateStaffID_Call) Run(run func(ctx context.Context, id int64, staffID int64)) *OrderItemRepo_UpdateStaffID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64))
	})
	return _c
}

func (_c *OrderItemRepo_UpdateStaffID_Call) Return(_a0 int64, _a1 error) *OrderItemRepo_UpdateStaffID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderItemRepo_UpdateStaffID_Call) RunAndReturn(run func(context.Context, int64, int64) (int64, error)) *OrderItemRepo_UpdateStaffID_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderItemRepo creates a new instance of OrderItemRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderItemRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderItemRepo {
	mock := &OrderItemRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
