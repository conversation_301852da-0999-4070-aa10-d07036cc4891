// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// OrderPaymentRepo is an autogenerated mock type for the OrderPaymentRepo type
type OrderPaymentRepo struct {
	mock.Mock
}

type OrderPaymentRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderPaymentRepo) EXPECT() *OrderPaymentRepo_Expecter {
	return &OrderPaymentRepo_Expecter{mock: &_m.Mock}
}

// BatchGet provides a mock function with given fields: ctx, ids
func (_m *OrderPaymentRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for BatchGet")
	}

	var r0 []*model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.OrderPayment, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.OrderPayment); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentRepo_BatchGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGet'
type OrderPaymentRepo_BatchGet_Call struct {
	*mock.Call
}

// BatchGet is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *OrderPaymentRepo_Expecter) BatchGet(ctx interface{}, ids interface{}) *OrderPaymentRepo_BatchGet_Call {
	return &OrderPaymentRepo_BatchGet_Call{Call: _e.mock.On("BatchGet", ctx, ids)}
}

func (_c *OrderPaymentRepo_BatchGet_Call) Run(run func(ctx context.Context, ids []int64)) *OrderPaymentRepo_BatchGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *OrderPaymentRepo_BatchGet_Call) Return(_a0 []*model.OrderPayment, _a1 error) *OrderPaymentRepo_BatchGet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentRepo_BatchGet_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.OrderPayment, error)) *OrderPaymentRepo_BatchGet_Call {
	_c.Call.Return(run)
	return _c
}

// CancelOrderPayment provides a mock function with given fields: ctx, orderPayment
func (_m *OrderPaymentRepo) CancelOrderPayment(ctx context.Context, orderPayment *model.OrderPayment) (int64, error) {
	ret := _m.Called(ctx, orderPayment)

	if len(ret) == 0 {
		panic("no return value specified for CancelOrderPayment")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderPayment) (int64, error)); ok {
		return rf(ctx, orderPayment)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderPayment) int64); ok {
		r0 = rf(ctx, orderPayment)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.OrderPayment) error); ok {
		r1 = rf(ctx, orderPayment)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentRepo_CancelOrderPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelOrderPayment'
type OrderPaymentRepo_CancelOrderPayment_Call struct {
	*mock.Call
}

// CancelOrderPayment is a helper method to define mock.On call
//   - ctx context.Context
//   - orderPayment *model.OrderPayment
func (_e *OrderPaymentRepo_Expecter) CancelOrderPayment(ctx interface{}, orderPayment interface{}) *OrderPaymentRepo_CancelOrderPayment_Call {
	return &OrderPaymentRepo_CancelOrderPayment_Call{Call: _e.mock.On("CancelOrderPayment", ctx, orderPayment)}
}

func (_c *OrderPaymentRepo_CancelOrderPayment_Call) Run(run func(ctx context.Context, orderPayment *model.OrderPayment)) *OrderPaymentRepo_CancelOrderPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.OrderPayment))
	})
	return _c
}

func (_c *OrderPaymentRepo_CancelOrderPayment_Call) Return(_a0 int64, _a1 error) *OrderPaymentRepo_CancelOrderPayment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentRepo_CancelOrderPayment_Call) RunAndReturn(run func(context.Context, *model.OrderPayment) (int64, error)) *OrderPaymentRepo_CancelOrderPayment_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, id
func (_m *OrderPaymentRepo) Get(ctx context.Context, id int64) (*model.OrderPayment, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.OrderPayment, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.OrderPayment); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type OrderPaymentRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *OrderPaymentRepo_Expecter) Get(ctx interface{}, id interface{}) *OrderPaymentRepo_Get_Call {
	return &OrderPaymentRepo_Get_Call{Call: _e.mock.On("Get", ctx, id)}
}

func (_c *OrderPaymentRepo_Get_Call) Run(run func(ctx context.Context, id int64)) *OrderPaymentRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderPaymentRepo_Get_Call) Return(_a0 *model.OrderPayment, _a1 error) *OrderPaymentRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentRepo_Get_Call) RunAndReturn(run func(context.Context, int64) (*model.OrderPayment, error)) *OrderPaymentRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *OrderPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.OrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.OrderPayment, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.OrderPayment); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type OrderPaymentRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderPaymentRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *OrderPaymentRepo_ListByOrderID_Call {
	return &OrderPaymentRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *OrderPaymentRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *OrderPaymentRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderPaymentRepo_ListByOrderID_Call) Return(_a0 []*model.OrderPayment, _a1 error) *OrderPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.OrderPayment, error)) *OrderPaymentRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRefund provides a mock function with given fields: ctx, refundOrderPayment
func (_m *OrderPaymentRepo) UpdateRefund(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) (int64, error) {
	ret := _m.Called(ctx, refundOrderPayment)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRefund")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment) (int64, error)); ok {
		return rf(ctx, refundOrderPayment)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment) int64); ok {
		r0 = rf(ctx, refundOrderPayment)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.RefundOrderPayment) error); ok {
		r1 = rf(ctx, refundOrderPayment)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPaymentRepo_UpdateRefund_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRefund'
type OrderPaymentRepo_UpdateRefund_Call struct {
	*mock.Call
}

// UpdateRefund is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderPayment *model.RefundOrderPayment
func (_e *OrderPaymentRepo_Expecter) UpdateRefund(ctx interface{}, refundOrderPayment interface{}) *OrderPaymentRepo_UpdateRefund_Call {
	return &OrderPaymentRepo_UpdateRefund_Call{Call: _e.mock.On("UpdateRefund", ctx, refundOrderPayment)}
}

func (_c *OrderPaymentRepo_UpdateRefund_Call) Run(run func(ctx context.Context, refundOrderPayment *model.RefundOrderPayment)) *OrderPaymentRepo_UpdateRefund_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrderPayment))
	})
	return _c
}

func (_c *OrderPaymentRepo_UpdateRefund_Call) Return(_a0 int64, _a1 error) *OrderPaymentRepo_UpdateRefund_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPaymentRepo_UpdateRefund_Call) RunAndReturn(run func(context.Context, *model.RefundOrderPayment) (int64, error)) *OrderPaymentRepo_UpdateRefund_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderPaymentRepo creates a new instance of OrderPaymentRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderPaymentRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderPaymentRepo {
	mock := &OrderPaymentRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
