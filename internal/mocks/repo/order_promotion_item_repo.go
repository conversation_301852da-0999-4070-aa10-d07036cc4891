// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// OrderPromotionItemRepo is an autogenerated mock type for the OrderPromotionItemRepo type
type OrderPromotionItemRepo struct {
	mock.Mock
}

type OrderPromotionItemRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderPromotionItemRepo) EXPECT() *OrderPromotionItemRepo_Expecter {
	return &OrderPromotionItemRepo_Expecter{mock: &_m.Mock}
}

// BatchCreate provides a mock function with given fields: ctx, orderPromotions
func (_m *OrderPromotionItemRepo) BatchCreate(ctx context.Context, orderPromotions []*model.OrderPromotionItem) error {
	ret := _m.Called(ctx, orderPromotions)

	if len(ret) == 0 {
		panic("no return value specified for Batch<PERSON>reate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.OrderPromotionItem) error); ok {
		r0 = rf(ctx, orderPromotions)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderPromotionItemRepo_BatchCreate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCreate'
type OrderPromotionItemRepo_BatchCreate_Call struct {
	*mock.Call
}

// BatchCreate is a helper method to define mock.On call
//   - ctx context.Context
//   - orderPromotions []*model.OrderPromotionItem
func (_e *OrderPromotionItemRepo_Expecter) BatchCreate(ctx interface{}, orderPromotions interface{}) *OrderPromotionItemRepo_BatchCreate_Call {
	return &OrderPromotionItemRepo_BatchCreate_Call{Call: _e.mock.On("BatchCreate", ctx, orderPromotions)}
}

func (_c *OrderPromotionItemRepo_BatchCreate_Call) Run(run func(ctx context.Context, orderPromotions []*model.OrderPromotionItem)) *OrderPromotionItemRepo_BatchCreate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*model.OrderPromotionItem))
	})
	return _c
}

func (_c *OrderPromotionItemRepo_BatchCreate_Call) Return(_a0 error) *OrderPromotionItemRepo_BatchCreate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderPromotionItemRepo_BatchCreate_Call) RunAndReturn(run func(context.Context, []*model.OrderPromotionItem) error) *OrderPromotionItemRepo_BatchCreate_Call {
	_c.Call.Return(run)
	return _c
}

// ListByPromotionIDs provides a mock function with given fields: ctx, promotionIDList
func (_m *OrderPromotionItemRepo) ListByPromotionIDs(ctx context.Context, promotionIDList []int64) ([]*model.OrderPromotionItem, error) {
	ret := _m.Called(ctx, promotionIDList)

	if len(ret) == 0 {
		panic("no return value specified for ListByPromotionIDs")
	}

	var r0 []*model.OrderPromotionItem
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.OrderPromotionItem, error)); ok {
		return rf(ctx, promotionIDList)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.OrderPromotionItem); ok {
		r0 = rf(ctx, promotionIDList)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPromotionItem)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, promotionIDList)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderPromotionItemRepo_ListByPromotionIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByPromotionIDs'
type OrderPromotionItemRepo_ListByPromotionIDs_Call struct {
	*mock.Call
}

// ListByPromotionIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - promotionIDList []int64
func (_e *OrderPromotionItemRepo_Expecter) ListByPromotionIDs(ctx interface{}, promotionIDList interface{}) *OrderPromotionItemRepo_ListByPromotionIDs_Call {
	return &OrderPromotionItemRepo_ListByPromotionIDs_Call{Call: _e.mock.On("ListByPromotionIDs", ctx, promotionIDList)}
}

func (_c *OrderPromotionItemRepo_ListByPromotionIDs_Call) Run(run func(ctx context.Context, promotionIDList []int64)) *OrderPromotionItemRepo_ListByPromotionIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *OrderPromotionItemRepo_ListByPromotionIDs_Call) Return(_a0 []*model.OrderPromotionItem, _a1 error) *OrderPromotionItemRepo_ListByPromotionIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderPromotionItemRepo_ListByPromotionIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.OrderPromotionItem, error)) *OrderPromotionItemRepo_ListByPromotionIDs_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderPromotionItemRepo creates a new instance of OrderPromotionItemRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderPromotionItemRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderPromotionItemRepo {
	mock := &OrderPromotionItemRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
