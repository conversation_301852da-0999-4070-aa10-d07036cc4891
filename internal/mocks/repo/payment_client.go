// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	decimal "github.com/shopspring/decimal"
	mock "github.com/stretchr/testify/mock"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"

	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"

	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

// PaymentClient is an autogenerated mock type for the PaymentClient type
type PaymentClient struct {
	mock.Mock
}

type PaymentClient_Expecter struct {
	mock *mock.Mock
}

func (_m *PaymentClient) EXPECT() *PaymentClient_Expecter {
	return &PaymentClient_Expecter{mock: &_m.Mock}
}

// CaptureByInvoiceID provides a mock function with given fields: ctx, businessID, invoiceID
func (_m *PaymentClient) CaptureByInvoiceID(ctx context.Context, businessID int64, invoiceID int64) error {
	ret := _m.Called(ctx, businessID, invoiceID)

	if len(ret) == 0 {
		panic("no return value specified for CaptureByInvoiceID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64) error); ok {
		r0 = rf(ctx, businessID, invoiceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PaymentClient_CaptureByInvoiceID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CaptureByInvoiceID'
type PaymentClient_CaptureByInvoiceID_Call struct {
	*mock.Call
}

// CaptureByInvoiceID is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
//   - invoiceID int64
func (_e *PaymentClient_Expecter) CaptureByInvoiceID(ctx interface{}, businessID interface{}, invoiceID interface{}) *PaymentClient_CaptureByInvoiceID_Call {
	return &PaymentClient_CaptureByInvoiceID_Call{Call: _e.mock.On("CaptureByInvoiceID", ctx, businessID, invoiceID)}
}

func (_c *PaymentClient_CaptureByInvoiceID_Call) Run(run func(ctx context.Context, businessID int64, invoiceID int64)) *PaymentClient_CaptureByInvoiceID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64))
	})
	return _c
}

func (_c *PaymentClient_CaptureByInvoiceID_Call) Return(_a0 error) *PaymentClient_CaptureByInvoiceID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *PaymentClient_CaptureByInvoiceID_Call) RunAndReturn(run func(context.Context, int64, int64) error) *PaymentClient_CaptureByInvoiceID_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRefundByOrder provides a mock function with given fields: ctx, orderID, reason, refundAmount, refundableChannels
func (_m *PaymentClient) CreateRefundByOrder(ctx context.Context, orderID int64, reason string, refundAmount decimal.Decimal, refundableChannels []*repo.RefundableChannel) error {
	ret := _m.Called(ctx, orderID, reason, refundAmount, refundableChannels)

	if len(ret) == 0 {
		panic("no return value specified for CreateRefundByOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string, decimal.Decimal, []*repo.RefundableChannel) error); ok {
		r0 = rf(ctx, orderID, reason, refundAmount, refundableChannels)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PaymentClient_CreateRefundByOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRefundByOrder'
type PaymentClient_CreateRefundByOrder_Call struct {
	*mock.Call
}

// CreateRefundByOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
//   - reason string
//   - refundAmount decimal.Decimal
//   - refundableChannels []*repo.RefundableChannel
func (_e *PaymentClient_Expecter) CreateRefundByOrder(ctx interface{}, orderID interface{}, reason interface{}, refundAmount interface{}, refundableChannels interface{}) *PaymentClient_CreateRefundByOrder_Call {
	return &PaymentClient_CreateRefundByOrder_Call{Call: _e.mock.On("CreateRefundByOrder", ctx, orderID, reason, refundAmount, refundableChannels)}
}

func (_c *PaymentClient_CreateRefundByOrder_Call) Run(run func(ctx context.Context, orderID int64, reason string, refundAmount decimal.Decimal, refundableChannels []*repo.RefundableChannel)) *PaymentClient_CreateRefundByOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string), args[3].(decimal.Decimal), args[4].([]*repo.RefundableChannel))
	})
	return _c
}

func (_c *PaymentClient_CreateRefundByOrder_Call) Return(_a0 error) *PaymentClient_CreateRefundByOrder_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *PaymentClient_CreateRefundByOrder_Call) RunAndReturn(run func(context.Context, int64, string, decimal.Decimal, []*repo.RefundableChannel) error) *PaymentClient_CreateRefundByOrder_Call {
	_c.Call.Return(run)
	return _c
}

// CreateRefundPayment provides a mock function with given fields: ctx, paymentID, reason, rop
func (_m *PaymentClient) CreateRefundPayment(ctx context.Context, paymentID int64, reason string, rop *model.RefundOrderPayment) (*repo.RefundPaymentBrief, error) {
	ret := _m.Called(ctx, paymentID, reason, rop)

	if len(ret) == 0 {
		panic("no return value specified for CreateRefundPayment")
	}

	var r0 *repo.RefundPaymentBrief
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string, *model.RefundOrderPayment) (*repo.RefundPaymentBrief, error)); ok {
		return rf(ctx, paymentID, reason, rop)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, string, *model.RefundOrderPayment) *repo.RefundPaymentBrief); ok {
		r0 = rf(ctx, paymentID, reason, rop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repo.RefundPaymentBrief)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, string, *model.RefundOrderPayment) error); ok {
		r1 = rf(ctx, paymentID, reason, rop)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentClient_CreateRefundPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRefundPayment'
type PaymentClient_CreateRefundPayment_Call struct {
	*mock.Call
}

// CreateRefundPayment is a helper method to define mock.On call
//   - ctx context.Context
//   - paymentID int64
//   - reason string
//   - rop *model.RefundOrderPayment
func (_e *PaymentClient_Expecter) CreateRefundPayment(ctx interface{}, paymentID interface{}, reason interface{}, rop interface{}) *PaymentClient_CreateRefundPayment_Call {
	return &PaymentClient_CreateRefundPayment_Call{Call: _e.mock.On("CreateRefundPayment", ctx, paymentID, reason, rop)}
}

func (_c *PaymentClient_CreateRefundPayment_Call) Run(run func(ctx context.Context, paymentID int64, reason string, rop *model.RefundOrderPayment)) *PaymentClient_CreateRefundPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string), args[3].(*model.RefundOrderPayment))
	})
	return _c
}

func (_c *PaymentClient_CreateRefundPayment_Call) Return(_a0 *repo.RefundPaymentBrief, _a1 error) *PaymentClient_CreateRefundPayment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *PaymentClient_CreateRefundPayment_Call) RunAndReturn(run func(context.Context, int64, string, *model.RefundOrderPayment) (*repo.RefundPaymentBrief, error)) *PaymentClient_CreateRefundPayment_Call {
	_c.Call.Return(run)
	return _c
}

// GetConvenienceFee provides a mock function with given fields: ctx, businessID, amount, pm
func (_m *PaymentClient) GetConvenienceFee(ctx context.Context, businessID int64, amount decimal.Decimal, pm paymentpb.StripePaymentMethod) (decimal.Decimal, error) {
	ret := _m.Called(ctx, businessID, amount, pm)

	if len(ret) == 0 {
		panic("no return value specified for GetConvenienceFee")
	}

	var r0 decimal.Decimal
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, decimal.Decimal, paymentpb.StripePaymentMethod) (decimal.Decimal, error)); ok {
		return rf(ctx, businessID, amount, pm)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, decimal.Decimal, paymentpb.StripePaymentMethod) decimal.Decimal); ok {
		r0 = rf(ctx, businessID, amount, pm)
	} else {
		r0 = ret.Get(0).(decimal.Decimal)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, decimal.Decimal, paymentpb.StripePaymentMethod) error); ok {
		r1 = rf(ctx, businessID, amount, pm)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentClient_GetConvenienceFee_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConvenienceFee'
type PaymentClient_GetConvenienceFee_Call struct {
	*mock.Call
}

// GetConvenienceFee is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
//   - amount decimal.Decimal
//   - pm paymentpb.StripePaymentMethod
func (_e *PaymentClient_Expecter) GetConvenienceFee(ctx interface{}, businessID interface{}, amount interface{}, pm interface{}) *PaymentClient_GetConvenienceFee_Call {
	return &PaymentClient_GetConvenienceFee_Call{Call: _e.mock.On("GetConvenienceFee", ctx, businessID, amount, pm)}
}

func (_c *PaymentClient_GetConvenienceFee_Call) Run(run func(ctx context.Context, businessID int64, amount decimal.Decimal, pm paymentpb.StripePaymentMethod)) *PaymentClient_GetConvenienceFee_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(decimal.Decimal), args[3].(paymentpb.StripePaymentMethod))
	})
	return _c
}

func (_c *PaymentClient_GetConvenienceFee_Call) Return(_a0 decimal.Decimal, _a1 error) *PaymentClient_GetConvenienceFee_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *PaymentClient_GetConvenienceFee_Call) RunAndReturn(run func(context.Context, int64, decimal.Decimal, paymentpb.StripePaymentMethod) (decimal.Decimal, error)) *PaymentClient_GetConvenienceFee_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaymentSetting provides a mock function with given fields: ctx, businessID
func (_m *PaymentClient) GetPaymentSetting(ctx context.Context, businessID int64) (*repo.PaymentSetting, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for GetPaymentSetting")
	}

	var r0 *repo.PaymentSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*repo.PaymentSetting, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *repo.PaymentSetting); ok {
		r0 = rf(ctx, businessID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repo.PaymentSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentClient_GetPaymentSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaymentSetting'
type PaymentClient_GetPaymentSetting_Call struct {
	*mock.Call
}

// GetPaymentSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
func (_e *PaymentClient_Expecter) GetPaymentSetting(ctx interface{}, businessID interface{}) *PaymentClient_GetPaymentSetting_Call {
	return &PaymentClient_GetPaymentSetting_Call{Call: _e.mock.On("GetPaymentSetting", ctx, businessID)}
}

func (_c *PaymentClient_GetPaymentSetting_Call) Run(run func(ctx context.Context, businessID int64)) *PaymentClient_GetPaymentSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *PaymentClient_GetPaymentSetting_Call) Return(_a0 *repo.PaymentSetting, _a1 error) *PaymentClient_GetPaymentSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *PaymentClient_GetPaymentSetting_Call) RunAndReturn(run func(context.Context, int64) (*repo.PaymentSetting, error)) *PaymentClient_GetPaymentSetting_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundPayment provides a mock function with given fields: ctx, rop
func (_m *PaymentClient) GetRefundPayment(ctx context.Context, rop *model.RefundOrderPayment) (*repo.RefundPaymentBrief, error) {
	ret := _m.Called(ctx, rop)

	if len(ret) == 0 {
		panic("no return value specified for GetRefundPayment")
	}

	var r0 *repo.RefundPaymentBrief
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment) (*repo.RefundPaymentBrief, error)); ok {
		return rf(ctx, rop)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrderPayment) *repo.RefundPaymentBrief); ok {
		r0 = rf(ctx, rop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repo.RefundPaymentBrief)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.RefundOrderPayment) error); ok {
		r1 = rf(ctx, rop)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentClient_GetRefundPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundPayment'
type PaymentClient_GetRefundPayment_Call struct {
	*mock.Call
}

// GetRefundPayment is a helper method to define mock.On call
//   - ctx context.Context
//   - rop *model.RefundOrderPayment
func (_e *PaymentClient_Expecter) GetRefundPayment(ctx interface{}, rop interface{}) *PaymentClient_GetRefundPayment_Call {
	return &PaymentClient_GetRefundPayment_Call{Call: _e.mock.On("GetRefundPayment", ctx, rop)}
}

func (_c *PaymentClient_GetRefundPayment_Call) Run(run func(ctx context.Context, rop *model.RefundOrderPayment)) *PaymentClient_GetRefundPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrderPayment))
	})
	return _c
}

func (_c *PaymentClient_GetRefundPayment_Call) Return(_a0 *repo.RefundPaymentBrief, _a1 error) *PaymentClient_GetRefundPayment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *PaymentClient_GetRefundPayment_Call) RunAndReturn(run func(context.Context, *model.RefundOrderPayment) (*repo.RefundPaymentBrief, error)) *PaymentClient_GetRefundPayment_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundableChannel provides a mock function with given fields: ctx, businessID, orderID, amount
func (_m *PaymentClient) GetRefundableChannel(ctx context.Context, businessID int64, orderID int64, amount decimal.Decimal) ([]*repo.RefundableChannel, error) {
	ret := _m.Called(ctx, businessID, orderID, amount)

	if len(ret) == 0 {
		panic("no return value specified for GetRefundableChannel")
	}

	var r0 []*repo.RefundableChannel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, decimal.Decimal) ([]*repo.RefundableChannel, error)); ok {
		return rf(ctx, businessID, orderID, amount)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, decimal.Decimal) []*repo.RefundableChannel); ok {
		r0 = rf(ctx, businessID, orderID, amount)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repo.RefundableChannel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, decimal.Decimal) error); ok {
		r1 = rf(ctx, businessID, orderID, amount)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentClient_GetRefundableChannel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundableChannel'
type PaymentClient_GetRefundableChannel_Call struct {
	*mock.Call
}

// GetRefundableChannel is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
//   - orderID int64
//   - amount decimal.Decimal
func (_e *PaymentClient_Expecter) GetRefundableChannel(ctx interface{}, businessID interface{}, orderID interface{}, amount interface{}) *PaymentClient_GetRefundableChannel_Call {
	return &PaymentClient_GetRefundableChannel_Call{Call: _e.mock.On("GetRefundableChannel", ctx, businessID, orderID, amount)}
}

func (_c *PaymentClient_GetRefundableChannel_Call) Run(run func(ctx context.Context, businessID int64, orderID int64, amount decimal.Decimal)) *PaymentClient_GetRefundableChannel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(decimal.Decimal))
	})
	return _c
}

func (_c *PaymentClient_GetRefundableChannel_Call) Return(_a0 []*repo.RefundableChannel, _a1 error) *PaymentClient_GetRefundableChannel_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *PaymentClient_GetRefundableChannel_Call) RunAndReturn(run func(context.Context, int64, int64, decimal.Decimal) ([]*repo.RefundableChannel, error)) *PaymentClient_GetRefundableChannel_Call {
	_c.Call.Return(run)
	return _c
}

// NewPaymentClient creates a new instance of PaymentClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPaymentClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *PaymentClient {
	mock := &PaymentClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
