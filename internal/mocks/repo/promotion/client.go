// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	decimal "github.com/shopspring/decimal"
	mock "github.com/stretchr/testify/mock"

	promotionpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1"

	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/promotion/v1"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// PreviewCoupons provides a mock function with given fields: ctx, customerID, couponUsages
func (_m *Client) PreviewCoupons(ctx context.Context, customerID int64, couponUsages []*promotionpb.CouponUsage) (*v1.PreviewCouponsResponse, error) {
	ret := _m.Called(ctx, customerID, couponUsages)

	if len(ret) == 0 {
		panic("no return value specified for PreviewCoupons")
	}

	var r0 *v1.PreviewCouponsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, []*promotionpb.CouponUsage) (*v1.PreviewCouponsResponse, error)); ok {
		return rf(ctx, customerID, couponUsages)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, []*promotionpb.CouponUsage) *v1.PreviewCouponsResponse); ok {
		r0 = rf(ctx, customerID, couponUsages)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.PreviewCouponsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, []*promotionpb.CouponUsage) error); ok {
		r1 = rf(ctx, customerID, couponUsages)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_PreviewCoupons_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewCoupons'
type Client_PreviewCoupons_Call struct {
	*mock.Call
}

// PreviewCoupons is a helper method to define mock.On call
//   - ctx context.Context
//   - customerID int64
//   - couponUsages []*promotionpb.CouponUsage
func (_e *Client_Expecter) PreviewCoupons(ctx interface{}, customerID interface{}, couponUsages interface{}) *Client_PreviewCoupons_Call {
	return &Client_PreviewCoupons_Call{Call: _e.mock.On("PreviewCoupons", ctx, customerID, couponUsages)}
}

func (_c *Client_PreviewCoupons_Call) Run(run func(ctx context.Context, customerID int64, couponUsages []*promotionpb.CouponUsage)) *Client_PreviewCoupons_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].([]*promotionpb.CouponUsage))
	})
	return _c
}

func (_c *Client_PreviewCoupons_Call) Return(_a0 *v1.PreviewCouponsResponse, _a1 error) *Client_PreviewCoupons_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_PreviewCoupons_Call) RunAndReturn(run func(context.Context, int64, []*promotionpb.CouponUsage) (*v1.PreviewCouponsResponse, error)) *Client_PreviewCoupons_Call {
	_c.Call.Return(run)
	return _c
}

// RecommendCoupons provides a mock function with given fields: ctx, customerID, targetItems
func (_m *Client) RecommendCoupons(ctx context.Context, customerID int64, targetItems []*promotionpb.CouponApplicationTarget) (*v1.RecommendCouponsResponse, error) {
	ret := _m.Called(ctx, customerID, targetItems)

	if len(ret) == 0 {
		panic("no return value specified for RecommendCoupons")
	}

	var r0 *v1.RecommendCouponsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, []*promotionpb.CouponApplicationTarget) (*v1.RecommendCouponsResponse, error)); ok {
		return rf(ctx, customerID, targetItems)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, []*promotionpb.CouponApplicationTarget) *v1.RecommendCouponsResponse); ok {
		r0 = rf(ctx, customerID, targetItems)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.RecommendCouponsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, []*promotionpb.CouponApplicationTarget) error); ok {
		r1 = rf(ctx, customerID, targetItems)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_RecommendCoupons_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecommendCoupons'
type Client_RecommendCoupons_Call struct {
	*mock.Call
}

// RecommendCoupons is a helper method to define mock.On call
//   - ctx context.Context
//   - customerID int64
//   - targetItems []*promotionpb.CouponApplicationTarget
func (_e *Client_Expecter) RecommendCoupons(ctx interface{}, customerID interface{}, targetItems interface{}) *Client_RecommendCoupons_Call {
	return &Client_RecommendCoupons_Call{Call: _e.mock.On("RecommendCoupons", ctx, customerID, targetItems)}
}

func (_c *Client_RecommendCoupons_Call) Run(run func(ctx context.Context, customerID int64, targetItems []*promotionpb.CouponApplicationTarget)) *Client_RecommendCoupons_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].([]*promotionpb.CouponApplicationTarget))
	})
	return _c
}

func (_c *Client_RecommendCoupons_Call) Return(_a0 *v1.RecommendCouponsResponse, _a1 error) *Client_RecommendCoupons_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_RecommendCoupons_Call) RunAndReturn(run func(context.Context, int64, []*promotionpb.CouponApplicationTarget) (*v1.RecommendCouponsResponse, error)) *Client_RecommendCoupons_Call {
	_c.Call.Return(run)
	return _c
}

// RedeemCoupons provides a mock function with given fields: ctx, orderID, customerID, appointmentID, orderSales, redeems
func (_m *Client) RedeemCoupons(ctx context.Context, orderID int64, customerID int64, appointmentID int64, orderSales decimal.Decimal, redeems []*promotionpb.CouponRedeem) (*v1.RedeemCouponsResponse, error) {
	ret := _m.Called(ctx, orderID, customerID, appointmentID, orderSales, redeems)

	if len(ret) == 0 {
		panic("no return value specified for RedeemCoupons")
	}

	var r0 *v1.RedeemCouponsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, int64, decimal.Decimal, []*promotionpb.CouponRedeem) (*v1.RedeemCouponsResponse, error)); ok {
		return rf(ctx, orderID, customerID, appointmentID, orderSales, redeems)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, int64, decimal.Decimal, []*promotionpb.CouponRedeem) *v1.RedeemCouponsResponse); ok {
		r0 = rf(ctx, orderID, customerID, appointmentID, orderSales, redeems)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.RedeemCouponsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, int64, decimal.Decimal, []*promotionpb.CouponRedeem) error); ok {
		r1 = rf(ctx, orderID, customerID, appointmentID, orderSales, redeems)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_RedeemCoupons_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RedeemCoupons'
type Client_RedeemCoupons_Call struct {
	*mock.Call
}

// RedeemCoupons is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
//   - customerID int64
//   - appointmentID int64
//   - orderSales decimal.Decimal
//   - redeems []*promotionpb.CouponRedeem
func (_e *Client_Expecter) RedeemCoupons(ctx interface{}, orderID interface{}, customerID interface{}, appointmentID interface{}, orderSales interface{}, redeems interface{}) *Client_RedeemCoupons_Call {
	return &Client_RedeemCoupons_Call{Call: _e.mock.On("RedeemCoupons", ctx, orderID, customerID, appointmentID, orderSales, redeems)}
}

func (_c *Client_RedeemCoupons_Call) Run(run func(ctx context.Context, orderID int64, customerID int64, appointmentID int64, orderSales decimal.Decimal, redeems []*promotionpb.CouponRedeem)) *Client_RedeemCoupons_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(int64), args[4].(decimal.Decimal), args[5].([]*promotionpb.CouponRedeem))
	})
	return _c
}

func (_c *Client_RedeemCoupons_Call) Return(_a0 *v1.RedeemCouponsResponse, _a1 error) *Client_RedeemCoupons_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_RedeemCoupons_Call) RunAndReturn(run func(context.Context, int64, int64, int64, decimal.Decimal, []*promotionpb.CouponRedeem) (*v1.RedeemCouponsResponse, error)) *Client_RedeemCoupons_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
