// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// RefundOrderRepo is an autogenerated mock type for the RefundOrderRepo type
type RefundOrderRepo struct {
	mock.Mock
}

type RefundOrderRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *RefundOrderRepo) EXPECT() *RefundOrderRepo_Expecter {
	return &RefundOrderRepo_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, refundOrder
func (_m *RefundOrderRepo) Create(ctx context.Context, refundOrder *model.RefundOrder) error {
	ret := _m.Called(ctx, refundOrder)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrder) error); ok {
		r0 = rf(ctx, refundOrder)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type RefundOrderRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrder *model.RefundOrder
func (_e *RefundOrderRepo_Expecter) Create(ctx interface{}, refundOrder interface{}) *RefundOrderRepo_Create_Call {
	return &RefundOrderRepo_Create_Call{Call: _e.mock.On("Create", ctx, refundOrder)}
}

func (_c *RefundOrderRepo_Create_Call) Run(run func(ctx context.Context, refundOrder *model.RefundOrder)) *RefundOrderRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrder))
	})
	return _c
}

func (_c *RefundOrderRepo_Create_Call) Return(_a0 error) *RefundOrderRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderRepo_Create_Call) RunAndReturn(run func(context.Context, *model.RefundOrder) error) *RefundOrderRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, id
func (_m *RefundOrderRepo) Get(ctx context.Context, id int64) (*model.RefundOrder, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.RefundOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.RefundOrder, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.RefundOrder); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.RefundOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type RefundOrderRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *RefundOrderRepo_Expecter) Get(ctx interface{}, id interface{}) *RefundOrderRepo_Get_Call {
	return &RefundOrderRepo_Get_Call{Call: _e.mock.On("Get", ctx, id)}
}

func (_c *RefundOrderRepo_Get_Call) Run(run func(ctx context.Context, id int64)) *RefundOrderRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderRepo_Get_Call) Return(_a0 *model.RefundOrder, _a1 error) *RefundOrderRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderRepo_Get_Call) RunAndReturn(run func(context.Context, int64) (*model.RefundOrder, error)) *RefundOrderRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetDetail provides a mock function with given fields: ctx, id
func (_m *RefundOrderRepo) GetDetail(ctx context.Context, id int64) (*model.RefundOrderDetail, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetDetail")
	}

	var r0 *model.RefundOrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.RefundOrderDetail, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.RefundOrderDetail); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.RefundOrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderRepo_GetDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDetail'
type RefundOrderRepo_GetDetail_Call struct {
	*mock.Call
}

// GetDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *RefundOrderRepo_Expecter) GetDetail(ctx interface{}, id interface{}) *RefundOrderRepo_GetDetail_Call {
	return &RefundOrderRepo_GetDetail_Call{Call: _e.mock.On("GetDetail", ctx, id)}
}

func (_c *RefundOrderRepo_GetDetail_Call) Run(run func(ctx context.Context, id int64)) *RefundOrderRepo_GetDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderRepo_GetDetail_Call) Return(_a0 *model.RefundOrderDetail, _a1 error) *RefundOrderRepo_GetDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderRepo_GetDetail_Call) RunAndReturn(run func(context.Context, int64) (*model.RefundOrderDetail, error)) *RefundOrderRepo_GetDetail_Call {
	_c.Call.Return(run)
	return _c
}

// GetForUpdate provides a mock function with given fields: ctx, id
func (_m *RefundOrderRepo) GetForUpdate(ctx context.Context, id int64) (*model.RefundOrder, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetForUpdate")
	}

	var r0 *model.RefundOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.RefundOrder, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.RefundOrder); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.RefundOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderRepo_GetForUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForUpdate'
type RefundOrderRepo_GetForUpdate_Call struct {
	*mock.Call
}

// GetForUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *RefundOrderRepo_Expecter) GetForUpdate(ctx interface{}, id interface{}) *RefundOrderRepo_GetForUpdate_Call {
	return &RefundOrderRepo_GetForUpdate_Call{Call: _e.mock.On("GetForUpdate", ctx, id)}
}

func (_c *RefundOrderRepo_GetForUpdate_Call) Run(run func(ctx context.Context, id int64)) *RefundOrderRepo_GetForUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderRepo_GetForUpdate_Call) Return(_a0 *model.RefundOrder, _a1 error) *RefundOrderRepo_GetForUpdate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderRepo_GetForUpdate_Call) RunAndReturn(run func(context.Context, int64) (*model.RefundOrder, error)) *RefundOrderRepo_GetForUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrder, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.RefundOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrder, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrder); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type RefundOrderRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *RefundOrderRepo_ListByOrderID_Call {
	return &RefundOrderRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *RefundOrderRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderRepo_ListByOrderID_Call) Return(_a0 []*model.RefundOrder, _a1 error) *RefundOrderRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrder, error)) *RefundOrderRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// ListDetailByOrderID provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderRepo) ListDetailByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderDetail, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListDetailByOrderID")
	}

	var r0 []*model.RefundOrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderDetail, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderDetail); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderRepo_ListDetailByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDetailByOrderID'
type RefundOrderRepo_ListDetailByOrderID_Call struct {
	*mock.Call
}

// ListDetailByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderRepo_Expecter) ListDetailByOrderID(ctx interface{}, orderID interface{}) *RefundOrderRepo_ListDetailByOrderID_Call {
	return &RefundOrderRepo_ListDetailByOrderID_Call{Call: _e.mock.On("ListDetailByOrderID", ctx, orderID)}
}

func (_c *RefundOrderRepo_ListDetailByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderRepo_ListDetailByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderRepo_ListDetailByOrderID_Call) Return(_a0 []*model.RefundOrderDetail, _a1 error) *RefundOrderRepo_ListDetailByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderRepo_ListDetailByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderDetail, error)) *RefundOrderRepo_ListDetailByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateCompleted provides a mock function with given fields: ctx, refundOrder
func (_m *RefundOrderRepo) UpdateCompleted(ctx context.Context, refundOrder *model.RefundOrder) error {
	ret := _m.Called(ctx, refundOrder)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCompleted")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrder) error); ok {
		r0 = rf(ctx, refundOrder)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RefundOrderRepo_UpdateCompleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateCompleted'
type RefundOrderRepo_UpdateCompleted_Call struct {
	*mock.Call
}

// UpdateCompleted is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrder *model.RefundOrder
func (_e *RefundOrderRepo_Expecter) UpdateCompleted(ctx interface{}, refundOrder interface{}) *RefundOrderRepo_UpdateCompleted_Call {
	return &RefundOrderRepo_UpdateCompleted_Call{Call: _e.mock.On("UpdateCompleted", ctx, refundOrder)}
}

func (_c *RefundOrderRepo_UpdateCompleted_Call) Run(run func(ctx context.Context, refundOrder *model.RefundOrder)) *RefundOrderRepo_UpdateCompleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrder))
	})
	return _c
}

func (_c *RefundOrderRepo_UpdateCompleted_Call) Return(_a0 error) *RefundOrderRepo_UpdateCompleted_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RefundOrderRepo_UpdateCompleted_Call) RunAndReturn(run func(context.Context, *model.RefundOrder) error) *RefundOrderRepo_UpdateCompleted_Call {
	_c.Call.Return(run)
	return _c
}

// NewRefundOrderRepo creates a new instance of RefundOrderRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRefundOrderRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *RefundOrderRepo {
	mock := &RefundOrderRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
