// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	mock "github.com/stretchr/testify/mock"
)

// TipsSplitStatusClient is an autogenerated mock type for the TipsSplitStatusClient type
type TipsSplitStatusClient struct {
	mock.Mock
}

type TipsSplitStatusClient_Expecter struct {
	mock *mock.Mock
}

func (_m *TipsSplitStatusClient) EXPECT() *TipsSplitStatusClient_Expecter {
	return &TipsSplitStatusClient_Expecter{mock: &_m.Mock}
}

// Del provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *TipsSplitStatusClient) Del(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for Del")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TipsSplitStatusClient_Del_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Del'
type TipsSplitStatusClient_Del_Call struct {
	*mock.Call
}

// Del is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *TipsSplitStatusClient_Expecter) Del(ctx interface{}, sourceType interface{}, sourceID interface{}) *TipsSplitStatusClient_Del_Call {
	return &TipsSplitStatusClient_Del_Call{Call: _e.mock.On("Del", ctx, sourceType, sourceID)}
}

func (_c *TipsSplitStatusClient_Del_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *TipsSplitStatusClient_Del_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *TipsSplitStatusClient_Del_Call) Return(_a0 error) *TipsSplitStatusClient_Del_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitStatusClient_Del_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) error) *TipsSplitStatusClient_Del_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *TipsSplitStatusClient) Get(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) (bool, error) {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) (bool, error)); ok {
		return rf(ctx, sourceType, sourceID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) bool); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r1 = rf(ctx, sourceType, sourceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TipsSplitStatusClient_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type TipsSplitStatusClient_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *TipsSplitStatusClient_Expecter) Get(ctx interface{}, sourceType interface{}, sourceID interface{}) *TipsSplitStatusClient_Get_Call {
	return &TipsSplitStatusClient_Get_Call{Call: _e.mock.On("Get", ctx, sourceType, sourceID)}
}

func (_c *TipsSplitStatusClient_Get_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *TipsSplitStatusClient_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *TipsSplitStatusClient_Get_Call) Return(_a0 bool, _a1 error) *TipsSplitStatusClient_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TipsSplitStatusClient_Get_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) (bool, error)) *TipsSplitStatusClient_Get_Call {
	_c.Call.Return(run)
	return _c
}

// Set provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *TipsSplitStatusClient) Set(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for Set")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TipsSplitStatusClient_Set_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Set'
type TipsSplitStatusClient_Set_Call struct {
	*mock.Call
}

// Set is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *TipsSplitStatusClient_Expecter) Set(ctx interface{}, sourceType interface{}, sourceID interface{}) *TipsSplitStatusClient_Set_Call {
	return &TipsSplitStatusClient_Set_Call{Call: _e.mock.On("Set", ctx, sourceType, sourceID)}
}

func (_c *TipsSplitStatusClient_Set_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *TipsSplitStatusClient_Set_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *TipsSplitStatusClient_Set_Call) Return(_a0 error) *TipsSplitStatusClient_Set_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitStatusClient_Set_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) error) *TipsSplitStatusClient_Set_Call {
	_c.Call.Return(run)
	return _c
}

// NewTipsSplitStatusClient creates a new instance of TipsSplitStatusClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTipsSplitStatusClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *TipsSplitStatusClient {
	mock := &TipsSplitStatusClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
