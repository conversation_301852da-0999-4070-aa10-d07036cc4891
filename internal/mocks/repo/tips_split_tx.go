// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	repo "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	mock "github.com/stretchr/testify/mock"
)

// TipsSplitTx is an autogenerated mock type for the TipsSplitTx type
type TipsSplitTx struct {
	mock.Mock
}

type TipsSplitTx_Expecter struct {
	mock *mock.Mock
}

func (_m *TipsSplitTx) EXPECT() *TipsSplitTx_Expecter {
	return &TipsSplitTx_Expecter{mock: &_m.Mock}
}

// OrderItemRepo provides a mock function with no fields
func (_m *TipsSplitTx) OrderItemRepo() repo.OrderItemRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OrderItemRepo")
	}

	var r0 repo.OrderItemRepo
	if rf, ok := ret.Get(0).(func() repo.OrderItemRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.OrderItemRepo)
		}
	}

	return r0
}

// TipsSplitTx_OrderItemRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OrderItemRepo'
type TipsSplitTx_OrderItemRepo_Call struct {
	*mock.Call
}

// OrderItemRepo is a helper method to define mock.On call
func (_e *TipsSplitTx_Expecter) OrderItemRepo() *TipsSplitTx_OrderItemRepo_Call {
	return &TipsSplitTx_OrderItemRepo_Call{Call: _e.mock.On("OrderItemRepo")}
}

func (_c *TipsSplitTx_OrderItemRepo_Call) Run(run func()) *TipsSplitTx_OrderItemRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TipsSplitTx_OrderItemRepo_Call) Return(_a0 repo.OrderItemRepo) *TipsSplitTx_OrderItemRepo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitTx_OrderItemRepo_Call) RunAndReturn(run func() repo.OrderItemRepo) *TipsSplitTx_OrderItemRepo_Call {
	_c.Call.Return(run)
	return _c
}

// TipsSplit provides a mock function with no fields
func (_m *TipsSplitTx) TipsSplit() repo.TipsSplitRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for TipsSplit")
	}

	var r0 repo.TipsSplitRepo
	if rf, ok := ret.Get(0).(func() repo.TipsSplitRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.TipsSplitRepo)
		}
	}

	return r0
}

// TipsSplitTx_TipsSplit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TipsSplit'
type TipsSplitTx_TipsSplit_Call struct {
	*mock.Call
}

// TipsSplit is a helper method to define mock.On call
func (_e *TipsSplitTx_Expecter) TipsSplit() *TipsSplitTx_TipsSplit_Call {
	return &TipsSplitTx_TipsSplit_Call{Call: _e.mock.On("TipsSplit")}
}

func (_c *TipsSplitTx_TipsSplit_Call) Run(run func()) *TipsSplitTx_TipsSplit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TipsSplitTx_TipsSplit_Call) Return(_a0 repo.TipsSplitRepo) *TipsSplitTx_TipsSplit_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitTx_TipsSplit_Call) RunAndReturn(run func() repo.TipsSplitRepo) *TipsSplitTx_TipsSplit_Call {
	_c.Call.Return(run)
	return _c
}

// TipsSplitDetailRepo provides a mock function with no fields
func (_m *TipsSplitTx) TipsSplitDetailRepo() repo.TipsSplitDetailRepo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for TipsSplitDetailRepo")
	}

	var r0 repo.TipsSplitDetailRepo
	if rf, ok := ret.Get(0).(func() repo.TipsSplitDetailRepo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repo.TipsSplitDetailRepo)
		}
	}

	return r0
}

// TipsSplitTx_TipsSplitDetailRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TipsSplitDetailRepo'
type TipsSplitTx_TipsSplitDetailRepo_Call struct {
	*mock.Call
}

// TipsSplitDetailRepo is a helper method to define mock.On call
func (_e *TipsSplitTx_Expecter) TipsSplitDetailRepo() *TipsSplitTx_TipsSplitDetailRepo_Call {
	return &TipsSplitTx_TipsSplitDetailRepo_Call{Call: _e.mock.On("TipsSplitDetailRepo")}
}

func (_c *TipsSplitTx_TipsSplitDetailRepo_Call) Run(run func()) *TipsSplitTx_TipsSplitDetailRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TipsSplitTx_TipsSplitDetailRepo_Call) Return(_a0 repo.TipsSplitDetailRepo) *TipsSplitTx_TipsSplitDetailRepo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TipsSplitTx_TipsSplitDetailRepo_Call) RunAndReturn(run func() repo.TipsSplitDetailRepo) *TipsSplitTx_TipsSplitDetailRepo_Call {
	_c.Call.Return(run)
	return _c
}

// NewTipsSplitTx creates a new instance of TipsSplitTx. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTipsSplitTx(t interface {
	mock.TestingT
	Cleanup(func())
}) *TipsSplitTx {
	mock := &TipsSplitTx{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
