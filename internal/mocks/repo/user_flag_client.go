// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// UserFlagClient is an autogenerated mock type for the UserFlagClient type
type UserFlagClient struct {
	mock.Mock
}

type UserFlagClient_Expecter struct {
	mock *mock.Mock
}

func (_m *UserFlagClient) EXPECT() *UserFlagClient_Expecter {
	return &UserFlagClient_Expecter{mock: &_m.Mock}
}

// ImmutableInvoice provides a mock function with given fields: ctx, companyID
func (_m *UserFlagClient) ImmutableInvoice(ctx context.Context, companyID int64) (bool, error) {
	ret := _m.Called(ctx, companyID)

	if len(ret) == 0 {
		panic("no return value specified for ImmutableInvoice")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (bool, error)); ok {
		return rf(ctx, companyID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) bool); ok {
		r0 = rf(ctx, companyID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, companyID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserFlagClient_ImmutableInvoice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ImmutableInvoice'
type UserFlagClient_ImmutableInvoice_Call struct {
	*mock.Call
}

// ImmutableInvoice is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
func (_e *UserFlagClient_Expecter) ImmutableInvoice(ctx interface{}, companyID interface{}) *UserFlagClient_ImmutableInvoice_Call {
	return &UserFlagClient_ImmutableInvoice_Call{Call: _e.mock.On("ImmutableInvoice", ctx, companyID)}
}

func (_c *UserFlagClient_ImmutableInvoice_Call) Run(run func(ctx context.Context, companyID int64)) *UserFlagClient_ImmutableInvoice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *UserFlagClient_ImmutableInvoice_Call) Return(_a0 bool, _a1 error) *UserFlagClient_ImmutableInvoice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserFlagClient_ImmutableInvoice_Call) RunAndReturn(run func(context.Context, int64) (bool, error)) *UserFlagClient_ImmutableInvoice_Call {
	_c.Call.Return(run)
	return _c
}

// SwitchedToNewInvoice provides a mock function with given fields: ctx, businessID
func (_m *UserFlagClient) SwitchedToNewInvoice(ctx context.Context, businessID int64) (bool, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for SwitchedToNewInvoice")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (bool, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) bool); ok {
		r0 = rf(ctx, businessID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserFlagClient_SwitchedToNewInvoice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SwitchedToNewInvoice'
type UserFlagClient_SwitchedToNewInvoice_Call struct {
	*mock.Call
}

// SwitchedToNewInvoice is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
func (_e *UserFlagClient_Expecter) SwitchedToNewInvoice(ctx interface{}, businessID interface{}) *UserFlagClient_SwitchedToNewInvoice_Call {
	return &UserFlagClient_SwitchedToNewInvoice_Call{Call: _e.mock.On("SwitchedToNewInvoice", ctx, businessID)}
}

func (_c *UserFlagClient_SwitchedToNewInvoice_Call) Run(run func(ctx context.Context, businessID int64)) *UserFlagClient_SwitchedToNewInvoice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *UserFlagClient_SwitchedToNewInvoice_Call) Return(_a0 bool, _a1 error) *UserFlagClient_SwitchedToNewInvoice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserFlagClient_SwitchedToNewInvoice_Call) RunAndReturn(run func(context.Context, int64) (bool, error)) *UserFlagClient_SwitchedToNewInvoice_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserFlagClient creates a new instance of UserFlagClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserFlagClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserFlagClient {
	mock := &UserFlagClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
