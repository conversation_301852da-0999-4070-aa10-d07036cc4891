// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	client "github.com/MoeGolibrary/moego-svc-order-v2/internal/script/delete_qb_invoices/client"
	mock "github.com/stretchr/testify/mock"
)

// QBClientOption is an autogenerated mock type for the QBClientOption type
type QBClientOption struct {
	mock.Mock
}

type QBClientOption_Expecter struct {
	mock *mock.Mock
}

func (_m *QBClientOption) EXPECT() *QBClientOption_Expecter {
	return &QBClientOption_Expecter{mock: &_m.Mock}
}

// Execute provides a mock function with given fields: _a0
func (_m *QBClientOption) Execute(_a0 *client.QBClient) {
	_m.Called(_a0)
}

// QBClientOption_Execute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Execute'
type QBClientOption_Execute_Call struct {
	*mock.Call
}

// Execute is a helper method to define mock.On call
//   - _a0 *client.QBClient
func (_e *QBClientOption_Expecter) Execute(_a0 interface{}) *QBClientOption_Execute_Call {
	return &QBClientOption_Execute_Call{Call: _e.mock.On("Execute", _a0)}
}

func (_c *QBClientOption_Execute_Call) Run(run func(_a0 *client.QBClient)) *QBClientOption_Execute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*client.QBClient))
	})
	return _c
}

func (_c *QBClientOption_Execute_Call) Return() *QBClientOption_Execute_Call {
	_c.Call.Return()
	return _c
}

func (_c *QBClientOption_Execute_Call) RunAndReturn(run func(*client.QBClient)) *QBClientOption_Execute_Call {
	_c.Run(run)
	return _c
}

// NewQBClientOption creates a new instance of QBClientOption. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewQBClientOption(t interface {
	mock.TestingT
	Cleanup(func())
}) *QBClientOption {
	mock := &QBClientOption{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
