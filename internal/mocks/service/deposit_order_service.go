// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

// DepositOrderService is an autogenerated mock type for the DepositOrderService type
type DepositOrderService struct {
	mock.Mock
}

type DepositOrderService_Expecter struct {
	mock *mock.Mock
}

func (_m *DepositOrderService) EXPECT() *DepositOrderService_Expecter {
	return &DepositOrderService_Expecter{mock: &_m.Mock}
}

// CreateDepositOrder provides a mock function with given fields: ctx, req
func (_m *DepositOrderService) CreateDepositOrder(ctx context.Context, req *ordersvcpb.CreateDepositOrderRequest) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateDepositOrder")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateDepositOrderRequest) (*model.OrderDetail, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.CreateDepositOrderRequest) *model.OrderDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.CreateDepositOrderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositOrderService_CreateDepositOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDepositOrder'
type DepositOrderService_CreateDepositOrder_Call struct {
	*mock.Call
}

// CreateDepositOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.CreateDepositOrderRequest
func (_e *DepositOrderService_Expecter) CreateDepositOrder(ctx interface{}, req interface{}) *DepositOrderService_CreateDepositOrder_Call {
	return &DepositOrderService_CreateDepositOrder_Call{Call: _e.mock.On("CreateDepositOrder", ctx, req)}
}

func (_c *DepositOrderService_CreateDepositOrder_Call) Run(run func(ctx context.Context, req *ordersvcpb.CreateDepositOrderRequest)) *DepositOrderService_CreateDepositOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.CreateDepositOrderRequest))
	})
	return _c
}

func (_c *DepositOrderService_CreateDepositOrder_Call) Return(_a0 *model.OrderDetail, _a1 error) *DepositOrderService_CreateDepositOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositOrderService_CreateDepositOrder_Call) RunAndReturn(run func(context.Context, *ordersvcpb.CreateDepositOrderRequest) (*model.OrderDetail, error)) *DepositOrderService_CreateDepositOrder_Call {
	_c.Call.Return(run)
	return _c
}

// GetDeductedDepositOrderID provides a mock function with given fields: ctx, destOrderID
func (_m *DepositOrderService) GetDeductedDepositOrderID(ctx context.Context, destOrderID int64) (int64, error) {
	ret := _m.Called(ctx, destOrderID)

	if len(ret) == 0 {
		panic("no return value specified for GetDeductedDepositOrderID")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (int64, error)); ok {
		return rf(ctx, destOrderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) int64); ok {
		r0 = rf(ctx, destOrderID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, destOrderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositOrderService_GetDeductedDepositOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeductedDepositOrderID'
type DepositOrderService_GetDeductedDepositOrderID_Call struct {
	*mock.Call
}

// GetDeductedDepositOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - destOrderID int64
func (_e *DepositOrderService_Expecter) GetDeductedDepositOrderID(ctx interface{}, destOrderID interface{}) *DepositOrderService_GetDeductedDepositOrderID_Call {
	return &DepositOrderService_GetDeductedDepositOrderID_Call{Call: _e.mock.On("GetDeductedDepositOrderID", ctx, destOrderID)}
}

func (_c *DepositOrderService_GetDeductedDepositOrderID_Call) Run(run func(ctx context.Context, destOrderID int64)) *DepositOrderService_GetDeductedDepositOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositOrderService_GetDeductedDepositOrderID_Call) Return(_a0 int64, _a1 error) *DepositOrderService_GetDeductedDepositOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositOrderService_GetDeductedDepositOrderID_Call) RunAndReturn(run func(context.Context, int64) (int64, error)) *DepositOrderService_GetDeductedDepositOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// GetDepositDetail provides a mock function with given fields: ctx, order
func (_m *DepositOrderService) GetDepositDetail(ctx context.Context, order *model.Order) (*model.DepositDetail, error) {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for GetDepositDetail")
	}

	var r0 *model.DepositDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) (*model.DepositDetail, error)); ok {
		return rf(ctx, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) *model.DepositDetail); ok {
		r0 = rf(ctx, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Order) error); ok {
		r1 = rf(ctx, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositOrderService_GetDepositDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDepositDetail'
type DepositOrderService_GetDepositDetail_Call struct {
	*mock.Call
}

// GetDepositDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *DepositOrderService_Expecter) GetDepositDetail(ctx interface{}, order interface{}) *DepositOrderService_GetDepositDetail_Call {
	return &DepositOrderService_GetDepositDetail_Call{Call: _e.mock.On("GetDepositDetail", ctx, order)}
}

func (_c *DepositOrderService_GetDepositDetail_Call) Run(run func(ctx context.Context, order *model.Order)) *DepositOrderService_GetDepositDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *DepositOrderService_GetDepositDetail_Call) Return(_a0 *model.DepositDetail, _a1 error) *DepositOrderService_GetDepositDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositOrderService_GetDepositDetail_Call) RunAndReturn(run func(context.Context, *model.Order) (*model.DepositDetail, error)) *DepositOrderService_GetDepositDetail_Call {
	_c.Call.Return(run)
	return _c
}

// GetDepositSummary provides a mock function with given fields: ctx, order
func (_m *DepositOrderService) GetDepositSummary(ctx context.Context, order *model.Order) (*ordersvcpb.GetDepositDetailResponse, error) {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for GetDepositSummary")
	}

	var r0 *ordersvcpb.GetDepositDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) (*ordersvcpb.GetDepositDetailResponse, error)); ok {
		return rf(ctx, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) *ordersvcpb.GetDepositDetailResponse); ok {
		r0 = rf(ctx, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.GetDepositDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Order) error); ok {
		r1 = rf(ctx, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositOrderService_GetDepositSummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDepositSummary'
type DepositOrderService_GetDepositSummary_Call struct {
	*mock.Call
}

// GetDepositSummary is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *DepositOrderService_Expecter) GetDepositSummary(ctx interface{}, order interface{}) *DepositOrderService_GetDepositSummary_Call {
	return &DepositOrderService_GetDepositSummary_Call{Call: _e.mock.On("GetDepositSummary", ctx, order)}
}

func (_c *DepositOrderService_GetDepositSummary_Call) Run(run func(ctx context.Context, order *model.Order)) *DepositOrderService_GetDepositSummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *DepositOrderService_GetDepositSummary_Call) Return(_a0 *ordersvcpb.GetDepositDetailResponse, _a1 error) *DepositOrderService_GetDepositSummary_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositOrderService_GetDepositSummary_Call) RunAndReturn(run func(context.Context, *model.Order) (*ordersvcpb.GetDepositDetailResponse, error)) *DepositOrderService_GetDepositSummary_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDepositOrderSource provides a mock function with given fields: ctx, req
func (_m *DepositOrderService) UpdateDepositOrderSource(ctx context.Context, req *ordersvcpb.UpdateDepositOrderSourceRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDepositOrderSource")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.UpdateDepositOrderSourceRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DepositOrderService_UpdateDepositOrderSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDepositOrderSource'
type DepositOrderService_UpdateDepositOrderSource_Call struct {
	*mock.Call
}

// UpdateDepositOrderSource is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.UpdateDepositOrderSourceRequest
func (_e *DepositOrderService_Expecter) UpdateDepositOrderSource(ctx interface{}, req interface{}) *DepositOrderService_UpdateDepositOrderSource_Call {
	return &DepositOrderService_UpdateDepositOrderSource_Call{Call: _e.mock.On("UpdateDepositOrderSource", ctx, req)}
}

func (_c *DepositOrderService_UpdateDepositOrderSource_Call) Run(run func(ctx context.Context, req *ordersvcpb.UpdateDepositOrderSourceRequest)) *DepositOrderService_UpdateDepositOrderSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.UpdateDepositOrderSourceRequest))
	})
	return _c
}

func (_c *DepositOrderService_UpdateDepositOrderSource_Call) Return(_a0 error) *DepositOrderService_UpdateDepositOrderSource_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DepositOrderService_UpdateDepositOrderSource_Call) RunAndReturn(run func(context.Context, *ordersvcpb.UpdateDepositOrderSourceRequest) error) *DepositOrderService_UpdateDepositOrderSource_Call {
	_c.Call.Return(run)
	return _c
}

// NewDepositOrderService creates a new instance of DepositOrderService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDepositOrderService(t interface {
	mock.TestingT
	Cleanup(func())
}) *DepositOrderService {
	mock := &DepositOrderService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
