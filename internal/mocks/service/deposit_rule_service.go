// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"

	service "github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

// DepositRuleService is an autogenerated mock type for the DepositRuleService type
type DepositRuleService struct {
	mock.Mock
}

type DepositRuleService_Expecter struct {
	mock *mock.Mock
}

func (_m *DepositRuleService) EXPECT() *DepositRuleService_Expecter {
	return &DepositRuleService_Expecter{mock: &_m.<PERSON>}
}

// CreateDepositRule provides a mock function with given fields: ctx, companyID, businessID, rule
func (_m *DepositRuleService) CreateDepositRule(ctx context.Context, companyID int64, businessID int64, rule *orderpb.CreateDepositRuleDef) (*model.DepositRule, error) {
	ret := _m.Called(ctx, companyID, businessID, rule)

	if len(ret) == 0 {
		panic("no return value specified for CreateDepositRule")
	}

	var r0 *model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) (*model.DepositRule, error)); ok {
		return rf(ctx, companyID, businessID, rule)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) *model.DepositRule); ok {
		r0 = rf(ctx, companyID, businessID, rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) error); ok {
		r1 = rf(ctx, companyID, businessID, rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRuleService_CreateDepositRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDepositRule'
type DepositRuleService_CreateDepositRule_Call struct {
	*mock.Call
}

// CreateDepositRule is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
//   - businessID int64
//   - rule *orderpb.CreateDepositRuleDef
func (_e *DepositRuleService_Expecter) CreateDepositRule(ctx interface{}, companyID interface{}, businessID interface{}, rule interface{}) *DepositRuleService_CreateDepositRule_Call {
	return &DepositRuleService_CreateDepositRule_Call{Call: _e.mock.On("CreateDepositRule", ctx, companyID, businessID, rule)}
}

func (_c *DepositRuleService_CreateDepositRule_Call) Run(run func(ctx context.Context, companyID int64, businessID int64, rule *orderpb.CreateDepositRuleDef)) *DepositRuleService_CreateDepositRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(*orderpb.CreateDepositRuleDef))
	})
	return _c
}

func (_c *DepositRuleService_CreateDepositRule_Call) Return(_a0 *model.DepositRule, _a1 error) *DepositRuleService_CreateDepositRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRuleService_CreateDepositRule_Call) RunAndReturn(run func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) (*model.DepositRule, error)) *DepositRuleService_CreateDepositRule_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDepositRule provides a mock function with given fields: ctx, ruleID
func (_m *DepositRuleService) DeleteDepositRule(ctx context.Context, ruleID int64) error {
	ret := _m.Called(ctx, ruleID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDepositRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, ruleID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DepositRuleService_DeleteDepositRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDepositRule'
type DepositRuleService_DeleteDepositRule_Call struct {
	*mock.Call
}

// DeleteDepositRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID int64
func (_e *DepositRuleService_Expecter) DeleteDepositRule(ctx interface{}, ruleID interface{}) *DepositRuleService_DeleteDepositRule_Call {
	return &DepositRuleService_DeleteDepositRule_Call{Call: _e.mock.On("DeleteDepositRule", ctx, ruleID)}
}

func (_c *DepositRuleService_DeleteDepositRule_Call) Run(run func(ctx context.Context, ruleID int64)) *DepositRuleService_DeleteDepositRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositRuleService_DeleteDepositRule_Call) Return(_a0 error) *DepositRuleService_DeleteDepositRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DepositRuleService_DeleteDepositRule_Call) RunAndReturn(run func(context.Context, int64) error) *DepositRuleService_DeleteDepositRule_Call {
	_c.Call.Return(run)
	return _c
}

// ListDepositRules provides a mock function with given fields: ctx, businessID
func (_m *DepositRuleService) ListDepositRules(ctx context.Context, businessID int64) ([]*model.DepositRule, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for ListDepositRules")
	}

	var r0 []*model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.DepositRule, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.DepositRule); ok {
		r0 = rf(ctx, businessID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRuleService_ListDepositRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDepositRules'
type DepositRuleService_ListDepositRules_Call struct {
	*mock.Call
}

// ListDepositRules is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
func (_e *DepositRuleService_Expecter) ListDepositRules(ctx interface{}, businessID interface{}) *DepositRuleService_ListDepositRules_Call {
	return &DepositRuleService_ListDepositRules_Call{Call: _e.mock.On("ListDepositRules", ctx, businessID)}
}

func (_c *DepositRuleService_ListDepositRules_Call) Run(run func(ctx context.Context, businessID int64)) *DepositRuleService_ListDepositRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositRuleService_ListDepositRules_Call) Return(_a0 []*model.DepositRule, _a1 error) *DepositRuleService_ListDepositRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRuleService_ListDepositRules_Call) RunAndReturn(run func(context.Context, int64) ([]*model.DepositRule, error)) *DepositRuleService_ListDepositRules_Call {
	_c.Call.Return(run)
	return _c
}

// MigrateToDepositRules provides a mock function with given fields: ctx, businessIDs
func (_m *DepositRuleService) MigrateToDepositRules(ctx context.Context, businessIDs []int64) ([]service.DepositRuleMigrationResult, error) {
	ret := _m.Called(ctx, businessIDs)

	if len(ret) == 0 {
		panic("no return value specified for MigrateToDepositRules")
	}

	var r0 []service.DepositRuleMigrationResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]service.DepositRuleMigrationResult, error)); ok {
		return rf(ctx, businessIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []service.DepositRuleMigrationResult); ok {
		r0 = rf(ctx, businessIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]service.DepositRuleMigrationResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, businessIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRuleService_MigrateToDepositRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MigrateToDepositRules'
type DepositRuleService_MigrateToDepositRules_Call struct {
	*mock.Call
}

// MigrateToDepositRules is a helper method to define mock.On call
//   - ctx context.Context
//   - businessIDs []int64
func (_e *DepositRuleService_Expecter) MigrateToDepositRules(ctx interface{}, businessIDs interface{}) *DepositRuleService_MigrateToDepositRules_Call {
	return &DepositRuleService_MigrateToDepositRules_Call{Call: _e.mock.On("MigrateToDepositRules", ctx, businessIDs)}
}

func (_c *DepositRuleService_MigrateToDepositRules_Call) Run(run func(ctx context.Context, businessIDs []int64)) *DepositRuleService_MigrateToDepositRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *DepositRuleService_MigrateToDepositRules_Call) Return(_a0 []service.DepositRuleMigrationResult, _a1 error) *DepositRuleService_MigrateToDepositRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRuleService_MigrateToDepositRules_Call) RunAndReturn(run func(context.Context, []int64) ([]service.DepositRuleMigrationResult, error)) *DepositRuleService_MigrateToDepositRules_Call {
	_c.Call.Return(run)
	return _c
}

// PreviewDepositOrder provides a mock function with given fields: ctx, req
func (_m *DepositRuleService) PreviewDepositOrder(ctx context.Context, req *ordersvcpb.PreviewDepositOrderRequest) (*model.OrderDetail, []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PreviewDepositOrder")
	}

	var r0 *model.OrderDetail
	var r1 []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) (*model.OrderDetail, []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) *model.OrderDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule); ok {
		r1 = rf(ctx, req)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) error); ok {
		r2 = rf(ctx, req)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// DepositRuleService_PreviewDepositOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewDepositOrder'
type DepositRuleService_PreviewDepositOrder_Call struct {
	*mock.Call
}

// PreviewDepositOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.PreviewDepositOrderRequest
func (_e *DepositRuleService_Expecter) PreviewDepositOrder(ctx interface{}, req interface{}) *DepositRuleService_PreviewDepositOrder_Call {
	return &DepositRuleService_PreviewDepositOrder_Call{Call: _e.mock.On("PreviewDepositOrder", ctx, req)}
}

func (_c *DepositRuleService_PreviewDepositOrder_Call) Run(run func(ctx context.Context, req *ordersvcpb.PreviewDepositOrderRequest)) *DepositRuleService_PreviewDepositOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.PreviewDepositOrderRequest))
	})
	return _c
}

func (_c *DepositRuleService_PreviewDepositOrder_Call) Return(_a0 *model.OrderDetail, _a1 []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule, _a2 error) *DepositRuleService_PreviewDepositOrder_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *DepositRuleService_PreviewDepositOrder_Call) RunAndReturn(run func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) (*model.OrderDetail, []*ordersvcpb.PreviewDepositOrderResponse_PriceItemByRule, error)) *DepositRuleService_PreviewDepositOrder_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDepositRule provides a mock function with given fields: ctx, ruleID, rule
func (_m *DepositRuleService) UpdateDepositRule(ctx context.Context, ruleID int64, rule *orderpb.UpdateDepositRuleDef) (*model.DepositRule, error) {
	ret := _m.Called(ctx, ruleID, rule)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDepositRule")
	}

	var r0 *model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *orderpb.UpdateDepositRuleDef) (*model.DepositRule, error)); ok {
		return rf(ctx, ruleID, rule)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *orderpb.UpdateDepositRuleDef) *model.DepositRule); ok {
		r0 = rf(ctx, ruleID, rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *orderpb.UpdateDepositRuleDef) error); ok {
		r1 = rf(ctx, ruleID, rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRuleService_UpdateDepositRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDepositRule'
type DepositRuleService_UpdateDepositRule_Call struct {
	*mock.Call
}

// UpdateDepositRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID int64
//   - rule *orderpb.UpdateDepositRuleDef
func (_e *DepositRuleService_Expecter) UpdateDepositRule(ctx interface{}, ruleID interface{}, rule interface{}) *DepositRuleService_UpdateDepositRule_Call {
	return &DepositRuleService_UpdateDepositRule_Call{Call: _e.mock.On("UpdateDepositRule", ctx, ruleID, rule)}
}

func (_c *DepositRuleService_UpdateDepositRule_Call) Run(run func(ctx context.Context, ruleID int64, rule *orderpb.UpdateDepositRuleDef)) *DepositRuleService_UpdateDepositRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*orderpb.UpdateDepositRuleDef))
	})
	return _c
}

func (_c *DepositRuleService_UpdateDepositRule_Call) Return(_a0 *model.DepositRule, _a1 error) *DepositRuleService_UpdateDepositRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRuleService_UpdateDepositRule_Call) RunAndReturn(run func(context.Context, int64, *orderpb.UpdateDepositRuleDef) (*model.DepositRule, error)) *DepositRuleService_UpdateDepositRule_Call {
	_c.Call.Return(run)
	return _c
}

// NewDepositRuleService creates a new instance of DepositRuleService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDepositRuleService(t interface {
	mock.TestingT
	Cleanup(func())
}) *DepositRuleService {
	mock := &DepositRuleService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
