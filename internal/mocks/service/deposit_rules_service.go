// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
)

// DepositRulesService is an autogenerated mock type for the DepositRulesService type
type DepositRulesService struct {
	mock.Mock
}

type DepositRulesService_Expecter struct {
	mock *mock.Mock
}

func (_m *DepositRulesService) EXPECT() *DepositRulesService_Expecter {
	return &DepositRulesService_Expecter{mock: &_m.Mock}
}

// CreateDepositRule provides a mock function with given fields: ctx, companyID, businessID, rule
func (_m *DepositRulesService) CreateDepositRule(ctx context.Context, companyID int64, businessID int64, rule *orderpb.CreateDepositRuleDef) (*model.DepositRule, error) {
	ret := _m.Called(ctx, companyID, businessID, rule)

	if len(ret) == 0 {
		panic("no return value specified for CreateDepositRule")
	}

	var r0 *model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) (*model.DepositRule, error)); ok {
		return rf(ctx, companyID, businessID, rule)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) *model.DepositRule); ok {
		r0 = rf(ctx, companyID, businessID, rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) error); ok {
		r1 = rf(ctx, companyID, businessID, rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRulesService_CreateDepositRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDepositRule'
type DepositRulesService_CreateDepositRule_Call struct {
	*mock.Call
}

// CreateDepositRule is a helper method to define mock.On call
//   - ctx context.Context
//   - companyID int64
//   - businessID int64
//   - rule *orderpb.CreateDepositRuleDef
func (_e *DepositRulesService_Expecter) CreateDepositRule(ctx interface{}, companyID interface{}, businessID interface{}, rule interface{}) *DepositRulesService_CreateDepositRule_Call {
	return &DepositRulesService_CreateDepositRule_Call{Call: _e.mock.On("CreateDepositRule", ctx, companyID, businessID, rule)}
}

func (_c *DepositRulesService_CreateDepositRule_Call) Run(run func(ctx context.Context, companyID int64, businessID int64, rule *orderpb.CreateDepositRuleDef)) *DepositRulesService_CreateDepositRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(*orderpb.CreateDepositRuleDef))
	})
	return _c
}

func (_c *DepositRulesService_CreateDepositRule_Call) Return(_a0 *model.DepositRule, _a1 error) *DepositRulesService_CreateDepositRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRulesService_CreateDepositRule_Call) RunAndReturn(run func(context.Context, int64, int64, *orderpb.CreateDepositRuleDef) (*model.DepositRule, error)) *DepositRulesService_CreateDepositRule_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteDepositRule provides a mock function with given fields: ctx, ruleID
func (_m *DepositRulesService) DeleteDepositRule(ctx context.Context, ruleID int64) error {
	ret := _m.Called(ctx, ruleID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteDepositRule")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, ruleID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DepositRulesService_DeleteDepositRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteDepositRule'
type DepositRulesService_DeleteDepositRule_Call struct {
	*mock.Call
}

// DeleteDepositRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID int64
func (_e *DepositRulesService_Expecter) DeleteDepositRule(ctx interface{}, ruleID interface{}) *DepositRulesService_DeleteDepositRule_Call {
	return &DepositRulesService_DeleteDepositRule_Call{Call: _e.mock.On("DeleteDepositRule", ctx, ruleID)}
}

func (_c *DepositRulesService_DeleteDepositRule_Call) Run(run func(ctx context.Context, ruleID int64)) *DepositRulesService_DeleteDepositRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositRulesService_DeleteDepositRule_Call) Return(_a0 error) *DepositRulesService_DeleteDepositRule_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DepositRulesService_DeleteDepositRule_Call) RunAndReturn(run func(context.Context, int64) error) *DepositRulesService_DeleteDepositRule_Call {
	_c.Call.Return(run)
	return _c
}

// ListDepositRules provides a mock function with given fields: ctx, businessID
func (_m *DepositRulesService) ListDepositRules(ctx context.Context, businessID int64) ([]*model.DepositRule, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for ListDepositRules")
	}

	var r0 []*model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.DepositRule, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.DepositRule); ok {
		r0 = rf(ctx, businessID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRulesService_ListDepositRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDepositRules'
type DepositRulesService_ListDepositRules_Call struct {
	*mock.Call
}

// ListDepositRules is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
func (_e *DepositRulesService_Expecter) ListDepositRules(ctx interface{}, businessID interface{}) *DepositRulesService_ListDepositRules_Call {
	return &DepositRulesService_ListDepositRules_Call{Call: _e.mock.On("ListDepositRules", ctx, businessID)}
}

func (_c *DepositRulesService_ListDepositRules_Call) Run(run func(ctx context.Context, businessID int64)) *DepositRulesService_ListDepositRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *DepositRulesService_ListDepositRules_Call) Return(_a0 []*model.DepositRule, _a1 error) *DepositRulesService_ListDepositRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRulesService_ListDepositRules_Call) RunAndReturn(run func(context.Context, int64) ([]*model.DepositRule, error)) *DepositRulesService_ListDepositRules_Call {
	_c.Call.Return(run)
	return _c
}

// PreviewDepositOrder provides a mock function with given fields: ctx, req
func (_m *DepositRulesService) PreviewDepositOrder(ctx context.Context, req *ordersvcpb.PreviewDepositOrderRequest) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PreviewDepositOrder")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) (*model.OrderDetail, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) *model.OrderDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRulesService_PreviewDepositOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewDepositOrder'
type DepositRulesService_PreviewDepositOrder_Call struct {
	*mock.Call
}

// PreviewDepositOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.PreviewDepositOrderRequest
func (_e *DepositRulesService_Expecter) PreviewDepositOrder(ctx interface{}, req interface{}) *DepositRulesService_PreviewDepositOrder_Call {
	return &DepositRulesService_PreviewDepositOrder_Call{Call: _e.mock.On("PreviewDepositOrder", ctx, req)}
}

func (_c *DepositRulesService_PreviewDepositOrder_Call) Run(run func(ctx context.Context, req *ordersvcpb.PreviewDepositOrderRequest)) *DepositRulesService_PreviewDepositOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.PreviewDepositOrderRequest))
	})
	return _c
}

func (_c *DepositRulesService_PreviewDepositOrder_Call) Return(_a0 *model.OrderDetail, _a1 error) *DepositRulesService_PreviewDepositOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRulesService_PreviewDepositOrder_Call) RunAndReturn(run func(context.Context, *ordersvcpb.PreviewDepositOrderRequest) (*model.OrderDetail, error)) *DepositRulesService_PreviewDepositOrder_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateDepositRule provides a mock function with given fields: ctx, ruleID, rule
func (_m *DepositRulesService) UpdateDepositRule(ctx context.Context, ruleID int64, rule *orderpb.UpdateDepositRuleDef) (*model.DepositRule, error) {
	ret := _m.Called(ctx, ruleID, rule)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDepositRule")
	}

	var r0 *model.DepositRule
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *orderpb.UpdateDepositRuleDef) (*model.DepositRule, error)); ok {
		return rf(ctx, ruleID, rule)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *orderpb.UpdateDepositRuleDef) *model.DepositRule); ok {
		r0 = rf(ctx, ruleID, rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositRule)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *orderpb.UpdateDepositRuleDef) error); ok {
		r1 = rf(ctx, ruleID, rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DepositRulesService_UpdateDepositRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDepositRule'
type DepositRulesService_UpdateDepositRule_Call struct {
	*mock.Call
}

// UpdateDepositRule is a helper method to define mock.On call
//   - ctx context.Context
//   - ruleID int64
//   - rule *orderpb.UpdateDepositRuleDef
func (_e *DepositRulesService_Expecter) UpdateDepositRule(ctx interface{}, ruleID interface{}, rule interface{}) *DepositRulesService_UpdateDepositRule_Call {
	return &DepositRulesService_UpdateDepositRule_Call{Call: _e.mock.On("UpdateDepositRule", ctx, ruleID, rule)}
}

func (_c *DepositRulesService_UpdateDepositRule_Call) Run(run func(ctx context.Context, ruleID int64, rule *orderpb.UpdateDepositRuleDef)) *DepositRulesService_UpdateDepositRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*orderpb.UpdateDepositRuleDef))
	})
	return _c
}

func (_c *DepositRulesService_UpdateDepositRule_Call) Return(_a0 *model.DepositRule, _a1 error) *DepositRulesService_UpdateDepositRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DepositRulesService_UpdateDepositRule_Call) RunAndReturn(run func(context.Context, int64, *orderpb.UpdateDepositRuleDef) (*model.DepositRule, error)) *DepositRulesService_UpdateDepositRule_Call {
	_c.Call.Return(run)
	return _c
}

// NewDepositRulesService creates a new instance of DepositRulesService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDepositRulesService(t interface {
	mock.TestingT
	Cleanup(func())
}) *DepositRulesService {
	mock := &DepositRulesService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
