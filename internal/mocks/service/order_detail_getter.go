// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// OrderDetailGetter is an autogenerated mock type for the OrderDetailGetter type
type OrderDetailGetter struct {
	mock.Mock
}

type OrderDetailGetter_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderDetailGetter) EXPECT() *OrderDetailGetter_Expecter {
	return &OrderDetailGetter_Expecter{mock: &_m.Mock}
}

// GetDetail provides a mock function with given fields: ctx, orderID
func (_m *OrderDetailGetter) GetDetail(ctx context.Context, orderID int64) (*model.OrderDetail, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for GetDetail")
	}

	var r0 *model.OrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.OrderDetail, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.OrderDetail); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.OrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderDetailGetter_GetDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDetail'
type OrderDetailGetter_GetDetail_Call struct {
	*mock.Call
}

// GetDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *OrderDetailGetter_Expecter) GetDetail(ctx interface{}, orderID interface{}) *OrderDetailGetter_GetDetail_Call {
	return &OrderDetailGetter_GetDetail_Call{Call: _e.mock.On("GetDetail", ctx, orderID)}
}

func (_c *OrderDetailGetter_GetDetail_Call) Run(run func(ctx context.Context, orderID int64)) *OrderDetailGetter_GetDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderDetailGetter_GetDetail_Call) Return(_a0 *model.OrderDetail, _a1 error) *OrderDetailGetter_GetDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderDetailGetter_GetDetail_Call) RunAndReturn(run func(context.Context, int64) (*model.OrderDetail, error)) *OrderDetailGetter_GetDetail_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderDetailGetter creates a new instance of OrderDetailGetter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderDetailGetter(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderDetailGetter {
	mock := &OrderDetailGetter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
