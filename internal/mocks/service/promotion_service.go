// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
)

// PromotionService is an autogenerated mock type for the PromotionService type
type PromotionService struct {
	mock.Mock
}

type PromotionService_Expecter struct {
	mock *mock.Mock
}

func (_m *PromotionService) EXPECT() *PromotionService_Expecter {
	return &PromotionService_Expecter{mock: &_m.Mock}
}

// AsyncRedeemCoupons provides a mock function with given fields: ctx, redeemParams
func (_m *PromotionService) AsyncRedeemCoupons(ctx context.Context, redeemParams *model.RedeemPromotionParams) {
	_m.Called(ctx, redeemParams)
}

// PromotionService_AsyncRedeemCoupons_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AsyncRedeemCoupons'
type PromotionService_AsyncRedeemCoupons_Call struct {
	*mock.Call
}

// AsyncRedeemCoupons is a helper method to define mock.On call
//   - ctx context.Context
//   - redeemParams *model.RedeemPromotionParams
func (_e *PromotionService_Expecter) AsyncRedeemCoupons(ctx interface{}, redeemParams interface{}) *PromotionService_AsyncRedeemCoupons_Call {
	return &PromotionService_AsyncRedeemCoupons_Call{Call: _e.mock.On("AsyncRedeemCoupons", ctx, redeemParams)}
}

func (_c *PromotionService_AsyncRedeemCoupons_Call) Run(run func(ctx context.Context, redeemParams *model.RedeemPromotionParams)) *PromotionService_AsyncRedeemCoupons_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RedeemPromotionParams))
	})
	return _c
}

func (_c *PromotionService_AsyncRedeemCoupons_Call) Return() *PromotionService_AsyncRedeemCoupons_Call {
	_c.Call.Return()
	return _c
}

func (_c *PromotionService_AsyncRedeemCoupons_Call) RunAndReturn(run func(context.Context, *model.RedeemPromotionParams)) *PromotionService_AsyncRedeemCoupons_Call {
	_c.Run(run)
	return _c
}

// PreviewCoupons provides a mock function with given fields: ctx, autoApply, cartItems, appliedPromotions, customerID
func (_m *PromotionService) PreviewCoupons(ctx context.Context, autoApply bool, cartItems []*ordersvcpb.PreviewCreateOrderRequest_CartItem, appliedPromotions *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions, customerID int64) ([]*model.OrderPromotion, error) {
	ret := _m.Called(ctx, autoApply, cartItems, appliedPromotions, customerID)

	if len(ret) == 0 {
		panic("no return value specified for PreviewCoupons")
	}

	var r0 []*model.OrderPromotion
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, bool, []*ordersvcpb.PreviewCreateOrderRequest_CartItem, *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions, int64) ([]*model.OrderPromotion, error)); ok {
		return rf(ctx, autoApply, cartItems, appliedPromotions, customerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, bool, []*ordersvcpb.PreviewCreateOrderRequest_CartItem, *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions, int64) []*model.OrderPromotion); ok {
		r0 = rf(ctx, autoApply, cartItems, appliedPromotions, customerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.OrderPromotion)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, bool, []*ordersvcpb.PreviewCreateOrderRequest_CartItem, *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions, int64) error); ok {
		r1 = rf(ctx, autoApply, cartItems, appliedPromotions, customerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PromotionService_PreviewCoupons_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewCoupons'
type PromotionService_PreviewCoupons_Call struct {
	*mock.Call
}

// PreviewCoupons is a helper method to define mock.On call
//   - ctx context.Context
//   - autoApply bool
//   - cartItems []*ordersvcpb.PreviewCreateOrderRequest_CartItem
//   - appliedPromotions *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions
//   - customerID int64
func (_e *PromotionService_Expecter) PreviewCoupons(ctx interface{}, autoApply interface{}, cartItems interface{}, appliedPromotions interface{}, customerID interface{}) *PromotionService_PreviewCoupons_Call {
	return &PromotionService_PreviewCoupons_Call{Call: _e.mock.On("PreviewCoupons", ctx, autoApply, cartItems, appliedPromotions, customerID)}
}

func (_c *PromotionService_PreviewCoupons_Call) Run(run func(ctx context.Context, autoApply bool, cartItems []*ordersvcpb.PreviewCreateOrderRequest_CartItem, appliedPromotions *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions, customerID int64)) *PromotionService_PreviewCoupons_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bool), args[2].([]*ordersvcpb.PreviewCreateOrderRequest_CartItem), args[3].(*ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions), args[4].(int64))
	})
	return _c
}

func (_c *PromotionService_PreviewCoupons_Call) Return(_a0 []*model.OrderPromotion, _a1 error) *PromotionService_PreviewCoupons_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *PromotionService_PreviewCoupons_Call) RunAndReturn(run func(context.Context, bool, []*ordersvcpb.PreviewCreateOrderRequest_CartItem, *ordersvcpb.PreviewCreateOrderRequest_AppliedPromotions, int64) ([]*model.OrderPromotion, error)) *PromotionService_PreviewCoupons_Call {
	_c.Call.Return(run)
	return _c
}

// RedeemCoupons provides a mock function with given fields: ctx, redeemParams
func (_m *PromotionService) RedeemCoupons(ctx context.Context, redeemParams *model.RedeemPromotionParams) error {
	ret := _m.Called(ctx, redeemParams)

	if len(ret) == 0 {
		panic("no return value specified for RedeemCoupons")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RedeemPromotionParams) error); ok {
		r0 = rf(ctx, redeemParams)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PromotionService_RedeemCoupons_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RedeemCoupons'
type PromotionService_RedeemCoupons_Call struct {
	*mock.Call
}

// RedeemCoupons is a helper method to define mock.On call
//   - ctx context.Context
//   - redeemParams *model.RedeemPromotionParams
func (_e *PromotionService_Expecter) RedeemCoupons(ctx interface{}, redeemParams interface{}) *PromotionService_RedeemCoupons_Call {
	return &PromotionService_RedeemCoupons_Call{Call: _e.mock.On("RedeemCoupons", ctx, redeemParams)}
}

func (_c *PromotionService_RedeemCoupons_Call) Run(run func(ctx context.Context, redeemParams *model.RedeemPromotionParams)) *PromotionService_RedeemCoupons_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RedeemPromotionParams))
	})
	return _c
}

func (_c *PromotionService_RedeemCoupons_Call) Return(_a0 error) *PromotionService_RedeemCoupons_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *PromotionService_RedeemCoupons_Call) RunAndReturn(run func(context.Context, *model.RedeemPromotionParams) error) *PromotionService_RedeemCoupons_Call {
	_c.Call.Return(run)
	return _c
}

// RetryRedeem provides a mock function with given fields: ctx
func (_m *PromotionService) RetryRedeem(ctx context.Context) {
	_m.Called(ctx)
}

// PromotionService_RetryRedeem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetryRedeem'
type PromotionService_RetryRedeem_Call struct {
	*mock.Call
}

// RetryRedeem is a helper method to define mock.On call
//   - ctx context.Context
func (_e *PromotionService_Expecter) RetryRedeem(ctx interface{}) *PromotionService_RetryRedeem_Call {
	return &PromotionService_RetryRedeem_Call{Call: _e.mock.On("RetryRedeem", ctx)}
}

func (_c *PromotionService_RetryRedeem_Call) Run(run func(ctx context.Context)) *PromotionService_RetryRedeem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *PromotionService_RetryRedeem_Call) Return() *PromotionService_RetryRedeem_Call {
	_c.Call.Return()
	return _c
}

func (_c *PromotionService_RetryRedeem_Call) RunAndReturn(run func(context.Context)) *PromotionService_RetryRedeem_Call {
	_c.Run(run)
	return _c
}

// NewPromotionService creates a new instance of PromotionService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPromotionService(t interface {
	mock.TestingT
	Cleanup(func())
}) *PromotionService {
	mock := &PromotionService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
