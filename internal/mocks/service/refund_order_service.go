// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"

	service "github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

// RefundOrderService is an autogenerated mock type for the RefundOrderService type
type RefundOrderService struct {
	mock.Mock
}

type RefundOrderService_Expecter struct {
	mock *mock.Mock
}

func (_m *RefundOrderService) EXPECT() *RefundOrderService_Expecter {
	return &RefundOrderService_Expecter{mock: &_m.Mock}
}

// GetDepositDetailForRefund provides a mock function with given fields: ctx, order
func (_m *RefundOrderService) GetDepositDetailForRefund(ctx context.Context, order *model.Order) (*model.DepositDetail, error) {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for GetDepositDetailForRefund")
	}

	var r0 *model.DepositDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) (*model.DepositDetail, error)); ok {
		return rf(ctx, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) *model.DepositDetail); ok {
		r0 = rf(ctx, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DepositDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Order) error); ok {
		r1 = rf(ctx, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_GetDepositDetailForRefund_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDepositDetailForRefund'
type RefundOrderService_GetDepositDetailForRefund_Call struct {
	*mock.Call
}

// GetDepositDetailForRefund is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *RefundOrderService_Expecter) GetDepositDetailForRefund(ctx interface{}, order interface{}) *RefundOrderService_GetDepositDetailForRefund_Call {
	return &RefundOrderService_GetDepositDetailForRefund_Call{Call: _e.mock.On("GetDepositDetailForRefund", ctx, order)}
}

func (_c *RefundOrderService_GetDepositDetailForRefund_Call) Run(run func(ctx context.Context, order *model.Order)) *RefundOrderService_GetDepositDetailForRefund_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *RefundOrderService_GetDepositDetailForRefund_Call) Return(_a0 *model.DepositDetail, _a1 error) *RefundOrderService_GetDepositDetailForRefund_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_GetDepositDetailForRefund_Call) RunAndReturn(run func(context.Context, *model.Order) (*model.DepositDetail, error)) *RefundOrderService_GetDepositDetailForRefund_Call {
	_c.Call.Return(run)
	return _c
}

// GetRefundDetail provides a mock function with given fields: ctx, refundID
func (_m *RefundOrderService) GetRefundDetail(ctx context.Context, refundID int64) (*model.RefundOrderDetail, error) {
	ret := _m.Called(ctx, refundID)

	if len(ret) == 0 {
		panic("no return value specified for GetRefundDetail")
	}

	var r0 *model.RefundOrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.RefundOrderDetail, error)); ok {
		return rf(ctx, refundID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.RefundOrderDetail); ok {
		r0 = rf(ctx, refundID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.RefundOrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, refundID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_GetRefundDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRefundDetail'
type RefundOrderService_GetRefundDetail_Call struct {
	*mock.Call
}

// GetRefundDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - refundID int64
func (_e *RefundOrderService_Expecter) GetRefundDetail(ctx interface{}, refundID interface{}) *RefundOrderService_GetRefundDetail_Call {
	return &RefundOrderService_GetRefundDetail_Call{Call: _e.mock.On("GetRefundDetail", ctx, refundID)}
}

func (_c *RefundOrderService_GetRefundDetail_Call) Run(run func(ctx context.Context, refundID int64)) *RefundOrderService_GetRefundDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_GetRefundDetail_Call) Return(_a0 *model.RefundOrderDetail, _a1 error) *RefundOrderService_GetRefundDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_GetRefundDetail_Call) RunAndReturn(run func(context.Context, int64) (*model.RefundOrderDetail, error)) *RefundOrderService_GetRefundDetail_Call {
	_c.Call.Return(run)
	return _c
}

// ListRefund provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderService) ListRefund(ctx context.Context, orderID int64) ([]*model.RefundOrder, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListRefund")
	}

	var r0 []*model.RefundOrder
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrder, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrder); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrder)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_ListRefund_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRefund'
type RefundOrderService_ListRefund_Call struct {
	*mock.Call
}

// ListRefund is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderService_Expecter) ListRefund(ctx interface{}, orderID interface{}) *RefundOrderService_ListRefund_Call {
	return &RefundOrderService_ListRefund_Call{Call: _e.mock.On("ListRefund", ctx, orderID)}
}

func (_c *RefundOrderService_ListRefund_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderService_ListRefund_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_ListRefund_Call) Return(_a0 []*model.RefundOrder, _a1 error) *RefundOrderService_ListRefund_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_ListRefund_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrder, error)) *RefundOrderService_ListRefund_Call {
	_c.Call.Return(run)
	return _c
}

// ListRefundDetail provides a mock function with given fields: ctx, orderID
func (_m *RefundOrderService) ListRefundDetail(ctx context.Context, orderID int64) ([]*model.RefundOrderDetail, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListRefundDetail")
	}

	var r0 []*model.RefundOrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderDetail, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderDetail); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_ListRefundDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRefundDetail'
type RefundOrderService_ListRefundDetail_Call struct {
	*mock.Call
}

// ListRefundDetail is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *RefundOrderService_Expecter) ListRefundDetail(ctx interface{}, orderID interface{}) *RefundOrderService_ListRefundDetail_Call {
	return &RefundOrderService_ListRefundDetail_Call{Call: _e.mock.On("ListRefundDetail", ctx, orderID)}
}

func (_c *RefundOrderService_ListRefundDetail_Call) Run(run func(ctx context.Context, orderID int64)) *RefundOrderService_ListRefundDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_ListRefundDetail_Call) Return(_a0 []*model.RefundOrderDetail, _a1 error) *RefundOrderService_ListRefundDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_ListRefundDetail_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderDetail, error)) *RefundOrderService_ListRefundDetail_Call {
	_c.Call.Return(run)
	return _c
}

// PreviewRefundOrder provides a mock function with given fields: ctx, orderDetail, req, og
func (_m *RefundOrderService) PreviewRefundOrder(ctx context.Context, orderDetail *model.OrderDetail, req *ordersvcpb.PreviewRefundOrderRequest, og service.OrderDetailGetter) (*ordersvcpb.PreviewRefundOrderResponse, error) {
	ret := _m.Called(ctx, orderDetail, req, og)

	if len(ret) == 0 {
		panic("no return value specified for PreviewRefundOrder")
	}

	var r0 *ordersvcpb.PreviewRefundOrderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *ordersvcpb.PreviewRefundOrderRequest, service.OrderDetailGetter) (*ordersvcpb.PreviewRefundOrderResponse, error)); ok {
		return rf(ctx, orderDetail, req, og)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *ordersvcpb.PreviewRefundOrderRequest, service.OrderDetailGetter) *ordersvcpb.PreviewRefundOrderResponse); ok {
		r0 = rf(ctx, orderDetail, req, og)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.PreviewRefundOrderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.OrderDetail, *ordersvcpb.PreviewRefundOrderRequest, service.OrderDetailGetter) error); ok {
		r1 = rf(ctx, orderDetail, req, og)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_PreviewRefundOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewRefundOrder'
type RefundOrderService_PreviewRefundOrder_Call struct {
	*mock.Call
}

// PreviewRefundOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderDetail *model.OrderDetail
//   - req *ordersvcpb.PreviewRefundOrderRequest
//   - og service.OrderDetailGetter
func (_e *RefundOrderService_Expecter) PreviewRefundOrder(ctx interface{}, orderDetail interface{}, req interface{}, og interface{}) *RefundOrderService_PreviewRefundOrder_Call {
	return &RefundOrderService_PreviewRefundOrder_Call{Call: _e.mock.On("PreviewRefundOrder", ctx, orderDetail, req, og)}
}

func (_c *RefundOrderService_PreviewRefundOrder_Call) Run(run func(ctx context.Context, orderDetail *model.OrderDetail, req *ordersvcpb.PreviewRefundOrderRequest, og service.OrderDetailGetter)) *RefundOrderService_PreviewRefundOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.OrderDetail), args[2].(*ordersvcpb.PreviewRefundOrderRequest), args[3].(service.OrderDetailGetter))
	})
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrder_Call) Return(_a0 *ordersvcpb.PreviewRefundOrderResponse, _a1 error) *RefundOrderService_PreviewRefundOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrder_Call) RunAndReturn(run func(context.Context, *model.OrderDetail, *ordersvcpb.PreviewRefundOrderRequest, service.OrderDetailGetter) (*ordersvcpb.PreviewRefundOrderResponse, error)) *RefundOrderService_PreviewRefundOrder_Call {
	_c.Call.Return(run)
	return _c
}

// PreviewRefundOrderPayments provides a mock function with given fields: ctx, req
func (_m *RefundOrderService) PreviewRefundOrderPayments(ctx context.Context, req *ordersvcpb.PreviewRefundOrderPaymentsRequest) (*ordersvcpb.PreviewRefundOrderPaymentsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PreviewRefundOrderPayments")
	}

	var r0 *ordersvcpb.PreviewRefundOrderPaymentsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PreviewRefundOrderPaymentsRequest) (*ordersvcpb.PreviewRefundOrderPaymentsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ordersvcpb.PreviewRefundOrderPaymentsRequest) *ordersvcpb.PreviewRefundOrderPaymentsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.PreviewRefundOrderPaymentsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ordersvcpb.PreviewRefundOrderPaymentsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_PreviewRefundOrderPayments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreviewRefundOrderPayments'
type RefundOrderService_PreviewRefundOrderPayments_Call struct {
	*mock.Call
}

// PreviewRefundOrderPayments is a helper method to define mock.On call
//   - ctx context.Context
//   - req *ordersvcpb.PreviewRefundOrderPaymentsRequest
func (_e *RefundOrderService_Expecter) PreviewRefundOrderPayments(ctx interface{}, req interface{}) *RefundOrderService_PreviewRefundOrderPayments_Call {
	return &RefundOrderService_PreviewRefundOrderPayments_Call{Call: _e.mock.On("PreviewRefundOrderPayments", ctx, req)}
}

func (_c *RefundOrderService_PreviewRefundOrderPayments_Call) Run(run func(ctx context.Context, req *ordersvcpb.PreviewRefundOrderPaymentsRequest)) *RefundOrderService_PreviewRefundOrderPayments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*ordersvcpb.PreviewRefundOrderPaymentsRequest))
	})
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrderPayments_Call) Return(_a0 *ordersvcpb.PreviewRefundOrderPaymentsResponse, _a1 error) *RefundOrderService_PreviewRefundOrderPayments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_PreviewRefundOrderPayments_Call) RunAndReturn(run func(context.Context, *ordersvcpb.PreviewRefundOrderPaymentsRequest) (*ordersvcpb.PreviewRefundOrderPaymentsResponse, error)) *RefundOrderService_PreviewRefundOrderPayments_Call {
	_c.Call.Return(run)
	return _c
}

// RefundOrder provides a mock function with given fields: ctx, orderDetail, req, og
func (_m *RefundOrderService) RefundOrder(ctx context.Context, orderDetail *model.OrderDetail, req *ordersvcpb.RefundOrderRequest, og service.OrderDetailGetter) (*ordersvcpb.RefundOrderResponse, error) {
	ret := _m.Called(ctx, orderDetail, req, og)

	if len(ret) == 0 {
		panic("no return value specified for RefundOrder")
	}

	var r0 *ordersvcpb.RefundOrderResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *ordersvcpb.RefundOrderRequest, service.OrderDetailGetter) (*ordersvcpb.RefundOrderResponse, error)); ok {
		return rf(ctx, orderDetail, req, og)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.OrderDetail, *ordersvcpb.RefundOrderRequest, service.OrderDetailGetter) *ordersvcpb.RefundOrderResponse); ok {
		r0 = rf(ctx, orderDetail, req, og)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ordersvcpb.RefundOrderResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.OrderDetail, *ordersvcpb.RefundOrderRequest, service.OrderDetailGetter) error); ok {
		r1 = rf(ctx, orderDetail, req, og)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundOrderService_RefundOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefundOrder'
type RefundOrderService_RefundOrder_Call struct {
	*mock.Call
}

// RefundOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - orderDetail *model.OrderDetail
//   - req *ordersvcpb.RefundOrderRequest
//   - og service.OrderDetailGetter
func (_e *RefundOrderService_Expecter) RefundOrder(ctx interface{}, orderDetail interface{}, req interface{}, og interface{}) *RefundOrderService_RefundOrder_Call {
	return &RefundOrderService_RefundOrder_Call{Call: _e.mock.On("RefundOrder", ctx, orderDetail, req, og)}
}

func (_c *RefundOrderService_RefundOrder_Call) Run(run func(ctx context.Context, orderDetail *model.OrderDetail, req *ordersvcpb.RefundOrderRequest, og service.OrderDetailGetter)) *RefundOrderService_RefundOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.OrderDetail), args[2].(*ordersvcpb.RefundOrderRequest), args[3].(service.OrderDetailGetter))
	})
	return _c
}

func (_c *RefundOrderService_RefundOrder_Call) Return(_a0 *ordersvcpb.RefundOrderResponse, _a1 error) *RefundOrderService_RefundOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RefundOrderService_RefundOrder_Call) RunAndReturn(run func(context.Context, *model.OrderDetail, *ordersvcpb.RefundOrderRequest, service.OrderDetailGetter) (*ordersvcpb.RefundOrderResponse, error)) *RefundOrderService_RefundOrder_Call {
	_c.Call.Return(run)
	return _c
}

// SyncRefundOrderPayment provides a mock function with given fields: ctx, refundOrderPaymentID
func (_m *RefundOrderService) SyncRefundOrderPayment(ctx context.Context, refundOrderPaymentID int64) (int64, int64, error) {
	ret := _m.Called(ctx, refundOrderPaymentID)

	if len(ret) == 0 {
		panic("no return value specified for SyncRefundOrderPayment")
	}

	var r0 int64
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (int64, int64, error)); ok {
		return rf(ctx, refundOrderPaymentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) int64); ok {
		r0 = rf(ctx, refundOrderPaymentID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) int64); ok {
		r1 = rf(ctx, refundOrderPaymentID)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int64) error); ok {
		r2 = rf(ctx, refundOrderPaymentID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// RefundOrderService_SyncRefundOrderPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SyncRefundOrderPayment'
type RefundOrderService_SyncRefundOrderPayment_Call struct {
	*mock.Call
}

// SyncRefundOrderPayment is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrderPaymentID int64
func (_e *RefundOrderService_Expecter) SyncRefundOrderPayment(ctx interface{}, refundOrderPaymentID interface{}) *RefundOrderService_SyncRefundOrderPayment_Call {
	return &RefundOrderService_SyncRefundOrderPayment_Call{Call: _e.mock.On("SyncRefundOrderPayment", ctx, refundOrderPaymentID)}
}

func (_c *RefundOrderService_SyncRefundOrderPayment_Call) Run(run func(ctx context.Context, refundOrderPaymentID int64)) *RefundOrderService_SyncRefundOrderPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *RefundOrderService_SyncRefundOrderPayment_Call) Return(total int64, synced int64, err error) *RefundOrderService_SyncRefundOrderPayment_Call {
	_c.Call.Return(total, synced, err)
	return _c
}

func (_c *RefundOrderService_SyncRefundOrderPayment_Call) RunAndReturn(run func(context.Context, int64) (int64, int64, error)) *RefundOrderService_SyncRefundOrderPayment_Call {
	_c.Call.Return(run)
	return _c
}

// NewRefundOrderService creates a new instance of RefundOrderService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRefundOrderService(t interface {
	mock.TestingT
	Cleanup(func())
}) *RefundOrderService {
	mock := &RefundOrderService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
