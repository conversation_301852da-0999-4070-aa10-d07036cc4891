package model

import (
	"database/sql"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	groomingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

const (
	OrderCancelReasonAppointmentCanceled = "CancelledAppointment"
	OrderCancelReasonInvoiceClosed       = "InvoiceClosed"
	OrderCancelReasonFulfillmentCanceled = "CancelledFulfillment"
)

const (
	OrderTitleFakeInvoice = "INVOICE"
)

type Order struct {
	ID           int64        `gorm:"primaryKey;autoIncrement"`
	OrderVersion OrderVersion // OrderVersion，订单本身的版本号，不同的 Order Version 有不同的状态机和处理逻辑.
	CompanyID    int64
	BusinessID   int64
	CustomerID   int64
	CreateBy     int64
	UpdateBy     int64
	GUID         string `gorm:"column:guid"`

	Title             string
	Description       string
	Status            orderpb.OrderStatus // OrderStatus 在 DB 里面是数字，加 Serializer 会导致写入错误
	FulfillmentStatus string
	PaymentStatus     orderpb.OrderModel_PaymentStatus `gorm:"serializer:proto_enum_legacy_order_payment_status"`

	OrderType         orderpb.OrderModel_OrderType `gorm:"serializer:proto_enum_legacy_order_type"`
	OrderRefID        int64
	ExtraChargeReason string

	// SourceType 这里有一个坑.
	// 本身应该是一个枚举值，在 PB 里面统一定义的是 NO_SHOW，但是在 Java 里面转换成字符串的时候变成了 NOSHOW.
	// Java 代码里面用的是不包含下划线的 NOSHOW.
	// 后续这里调整字段名字的时候需要注意这里的兼容性问题.
	SourceType   string       // 订单来源 appointment / noshow / product / package 等.
	SourceID     int64        // 结合 SourceType 存放不同的 ID.
	Version      int32        // 订单被修改的次数，区别于 OrderVersion.
	TaxRoundMode TaxRoundMode `gorm:"column:tax_round_mod"` // 税的舍入模式

	CurrencyCode    string
	LineItemTypes   int32 // 该订单下未被删除的 OrderItem 的 Type 的 bit flags.
	TipsAmount      decimal.Decimal
	TaxAmount       decimal.Decimal
	DiscountAmount  decimal.Decimal
	DepositAmount   decimal.Decimal
	ConvenienceFee  decimal.Decimal `gorm:"column:extra_fee_amount"`
	SubTotalAmount  decimal.Decimal
	TipsBasedAmount decimal.Decimal
	TotalAmount     decimal.Decimal
	PaidAmount      decimal.Decimal
	RemainAmount    decimal.Decimal
	RefundedAmount  decimal.Decimal
	CompleteTime    sql.NullTime
	CreateTime      sql.NullTime `gorm:"autoCreateTime"`
	UpdateTime      sql.NullTime `gorm:"autoUpdateTime"`

	// RefundableModes 是计算出来的信息.
	// TODO(yunxiang)： 这里有点奇怪，放在了基础模型中，但是是由 Detail 的信息计算的.
	RefundableModes []orderpb.RefundMode `gorm:"-"`
}

func (od *Order) IsExtra() bool {
	if od == nil {
		return false
	}

	return od.OrderType == orderpb.OrderModel_EXTRA
}

func (od *Order) IsOrigin() bool {
	if od == nil {
		return false
	}

	return od.OrderType == orderpb.OrderModel_ORIGIN
}

func (od *Order) IsTip() bool {
	if od == nil {
		return false
	}

	return od.OrderType == orderpb.OrderModel_TIP
}

func (od *Order) GetRefundableAmount() decimal.Decimal {
	if od == nil {
		return decimal.Zero
	}

	return od.PaidAmount.Sub(od.RefundedAmount)
}

func (od *Order) SupportOrderPayment() bool {
	if od == nil {
		return false
	}

	return od.OrderVersion.GtEq(OrderVersionRefund)
}

func (od *Order) IsNoShow() bool {
	// TODO(yunxiang): use pb.
	const sourceTypeNoShow = "noshow"

	if od == nil {
		return false
	}

	return od.SourceType == sourceTypeNoShow
}

func (od *Order) IsAppointment() bool {
	const sourceTypeAppointment = "appointment"

	if od == nil {
		return false
	}

	return od.SourceType == sourceTypeAppointment
}

func (od *Order) IsDeposit() bool {
	if od == nil {
		return false
	}

	return od.OrderType == orderpb.OrderModel_DEPOSIT
}

func (od *Order) IsCanceled() bool {
	return od.Status == orderpb.OrderStatus_REMOVED
}

func (od *Order) IsCompleted() bool {
	return od.Status == orderpb.OrderStatus_COMPLETED
}

func (od *Order) TransitStatus(status orderpb.OrderStatus) {
	od.Status = status

	if status == orderpb.OrderStatus_COMPLETED {
		od.CompleteTime = sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		}
	}
}

func (od *Order) IsFinalStatus() bool {
	return od.IsCanceled() || od.IsCompleted()
}

func (od *Order) ToPB() *orderpb.OrderModelV1 {
	return &orderpb.OrderModelV1{
		Id:                od.ID,
		BusinessId:        od.BusinessID,
		CustomerId:        od.CustomerID,
		Status:            od.Status,
		SourceType:        od.SourceType,
		PaymentStatus:     od.PaymentStatus,
		Guid:              od.GUID,
		SourceId:          od.SourceID,
		LineItemTypes:     od.LineItemTypes,
		Version:           od.Version,
		TipsAmount:        money.FromDecimal(od.TipsAmount, od.CurrencyCode),
		TaxAmount:         money.FromDecimal(od.TaxAmount, od.CurrencyCode),
		DiscountAmount:    money.FromDecimal(od.DiscountAmount, od.CurrencyCode),
		DepositAmount:     money.FromDecimal(od.DepositAmount, od.CurrencyCode),
		ConvenienceFee:    money.FromDecimal(od.ConvenienceFee, od.CurrencyCode),
		SubTotalAmount:    money.FromDecimal(od.SubTotalAmount, od.CurrencyCode),
		TipsBasedAmount:   money.FromDecimal(od.TipsBasedAmount, od.CurrencyCode),
		TotalAmount:       money.FromDecimal(od.TotalAmount, od.CurrencyCode),
		PaidAmount:        money.FromDecimal(od.PaidAmount, od.CurrencyCode),
		RemainAmount:      money.FromDecimal(od.RemainAmount, od.CurrencyCode),
		RefundedAmount:    money.FromDecimal(od.RefundedAmount, od.CurrencyCode),
		Title:             od.Title,
		CreateBy:          od.CreateBy,
		UpdateBy:          od.UpdateBy,
		CreateTime:        nullTimeToUnix(od.CreateTime),
		UpdateTime:        nullTimeToUnix(od.UpdateTime),
		FulfillmentStatus: od.FulfillmentStatus,
		Description:       od.Description,
		LineTaxes:         nil,
		LineDiscounts:     nil,
		LineExtraFees:     nil,
		Source:            groomingpb.AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED,
		CompanyId:         od.CompanyID,
		OrderType:         od.OrderType,
		CompleteTime:      nullTimeToUnix(od.CompleteTime),
		OrderRefId:        od.OrderRefID,
		ExtraChargeReason: od.ExtraChargeReason,
		OrderVersion:      od.OrderVersion.ToPB(),
		HasExtraOrder:     false,
		RefundableModes:   od.RefundableModes,
	}
}

func (od *Order) GetTipsAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.TipsAmount)
}

func (od *Order) GetTaxAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.TaxAmount)
}

func (od *Order) GetDiscountAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.DiscountAmount)
}

func (od *Order) GetConvenienceFee() decimal.Decimal {
	return compatibleWithLegacyZero(od.ConvenienceFee)
}

func (od *Order) GetSubTotalAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.SubTotalAmount)
}

func (od *Order) GetTipsBasedAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.TipsBasedAmount)
}

func (od *Order) GetTotalAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.TotalAmount)
}

func (od *Order) GetPaidAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.PaidAmount)
}

func (od *Order) GetRemainAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.RemainAmount)
}

func (od *Order) GetRefundedAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.RefundedAmount)
}

func (od *Order) GetDepositAmount() decimal.Decimal {
	return compatibleWithLegacyZero(od.DepositAmount)
}

type OrderDetail struct {
	Order               *Order
	OrderItems          []*OrderItem
	OrderDiscount       []*OrderLineDiscount
	OrderPayments       []*OrderPayment
	RefundOrderPayments []*RefundOrderPayment
	OrderPromotions     []*OrderPromotion
	DepositChangeLog    *DepositChangeLog
}

func (od *OrderDetail) GetID() int64 {
	if od == nil {
		return 0
	}

	return od.Order.ID
}

func (od *OrderDetail) GetCustomerID() int64 {
	if od == nil {
		return 0
	}

	return od.Order.CustomerID
}

func (od *OrderDetail) GetAppointmentID() int64 {
	if od == nil {
		return 0
	}

	if od.Order.IsAppointment() {
		return od.Order.SourceID
	}

	return 0
}

func (od *OrderDetail) GetTotalAmount() decimal.Decimal {
	if od == nil {
		return decimal.Decimal{}
	}

	return od.Order.TotalAmount
}

func (od *OrderDetail) IsExtra() bool {
	if od == nil {
		return false
	}

	return od.Order.IsExtra()
}

func (od *OrderDetail) IsDeposit() bool {
	if od == nil {
		return false
	}

	return od.Order.IsDeposit()
}

func (od *OrderDetail) IsComplete() bool {
	if od == nil || od.Order == nil {
		return false
	}

	return od.Order.IsCompleted()
}

func (od *OrderDetail) IsCanceled() bool {
	if od == nil || od.Order == nil {
		return false
	}

	return od.Order.IsCanceled()
}

func (od *OrderDetail) IsPartialPaid() bool {
	if od == nil || od.Order == nil {
		return false
	}

	return od.Order.PaidAmount.LessThan(od.Order.TotalAmount)
}

func (od *OrderDetail) OrderVersion() OrderVersion {
	if od == nil || od.Order == nil {
		return OrderVersionLegacy
	}

	return od.Order.OrderVersion
}

func (od *OrderDetail) ToPB() *orderpb.OrderDetailModelV1 {
	if od == nil {
		return nil
	}

	return &orderpb.OrderDetailModelV1{
		Order:      od.Order.ToPB(),
		OrderItems: lo.Map(od.OrderItems, func(item *OrderItem, _ int) *orderpb.OrderItemModel { return item.ToPB() }),
		TipsDetail: nil,
		OrderPayments: lo.Map(
			od.OrderPayments,
			func(payment *OrderPayment, _ int) *orderpb.OrderPaymentModel { return payment.ToPB() },
		),
		RefundOrderPayments: lo.Map(
			od.RefundOrderPayments,
			func(payment *RefundOrderPayment, _ int) *orderpb.RefundOrderPaymentModel { return payment.ToPB() },
		),
		OrderDiscounts: lo.Map(
			od.OrderDiscount,
			func(discount *OrderLineDiscount, _ int) *orderpb.OrderLineDiscountModelV1 {
				return discount.ToPB(od.Order.CurrencyCode)
			},
		),
		OrderPromotions: lo.Map(
			od.OrderPromotions,
			func(op *OrderPromotion, _ int) *orderpb.OrderPromotionModel {
				return op.ToPB()
			},
		),
	}
}

func (od *OrderDetail) IsAppliedPackage() bool {
	if od == nil || od.Order == nil {
		return false
	}

	for _, item := range od.OrderItems {
		if item.IsAppliedPackage() {
			return true
		}
	}

	return false
}

// NewEmptyOrder 返回一个 0 元的，空订单.
func NewEmptyOrder(
	orderType orderpb.OrderModel_OrderType,
	sourceType orderpb.OrderSourceType, sourceID int64,
	companyID, businessID, customerID, staffID int64,
) *Order {
	return &Order{
		ID: 0,
		// 默认是最新的稳定版本.
		OrderVersion:      OrderVersionStableLatest,
		CompanyID:         companyID,
		BusinessID:        businessID,
		CustomerID:        customerID,
		CreateBy:          staffID,
		UpdateBy:          staffID,
		Title:             "",
		Description:       "",
		GUID:              strings.ReplaceAll(uuid.New().String(), "-", ""),
		Status:            orderpb.OrderStatus_CREATED,
		FulfillmentStatus: orderpb.OrderModel_INIT.String(),
		PaymentStatus:     orderpb.OrderModel_UNPAID,
		OrderType:         orderType,
		// 当前场景一定是第一个订单.
		OrderRefID:        0,
		ExtraChargeReason: "",
		SourceType:        FormatSourceType(sourceType),
		SourceID:          sourceID,
		Version:           0,
		// 新订单的 Tax 都应该是四舍五入.
		TaxRoundMode:    TaxRoundModeHalfUp,
		CurrencyCode:    "",
		LineItemTypes:   0,
		TipsAmount:      decimal.Zero,
		TaxAmount:       decimal.Zero,
		DiscountAmount:  decimal.Zero,
		DepositAmount:   decimal.Zero,
		ConvenienceFee:  decimal.Zero,
		SubTotalAmount:  decimal.Zero,
		TipsBasedAmount: decimal.Zero,
		TotalAmount:     decimal.Zero,
		PaidAmount:      decimal.Zero,
		RemainAmount:    decimal.Zero,
		RefundedAmount:  decimal.Zero,
		CompleteTime:    sql.NullTime{},
		CreateTime:      sql.NullTime{},
		UpdateTime:      sql.NullTime{},
		RefundableModes: nil,
	}
}
