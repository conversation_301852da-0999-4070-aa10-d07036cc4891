package model

import (
	"database/sql"
	"strings"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

// copy from com.moego.common.enums.order.OrderItemType
const (
	ItemTypeService           = "service"
	ItemTypeProduct           = "product"
	ItemTypePackage           = "package"
	ItemTypeNoShow            = "noshow"
	ItemTypeServiceCharge     = "service_charge"
	ItemTypeEvaluationService = "evaluation_service"
	ItemTypeCancellationFee   = "cancellation_fee"
	ItemTypeMembershipProduct = "membership_product"
	ItemTypeDeposit           = "deposit"
)

type OrderItem struct {
	ID         int64 `gorm:"primaryKey;autoIncrement"`
	OrderID    int64
	BusinessID int64
	StaffID    int64
	// 从 appointment 数据反查出来的，后续考虑从 order 移除。
	StaffIDs []int64 `gorm:"-"`
	ItemType string  `gorm:"column:type"`
	// 结合 ItemType 存储不同的 ID.
	ObjectID int64
	// 外部 UUID，常见场景是 petDetailID, fulfillment ID 等.
	ExternalUUID string

	Name        string
	Description string
	UnitPrice   decimal.Decimal
	// 当有 SubTotalItems 时， UnitPrice 由 SubTotalAmount / Quantity 计算而来.
	// 在扩展表里，不在 Item 本身.
	SubTotalItems []*PriceItem `gorm:"-"`

	Quantity          int32
	PurchasedQuantity int32

	PetID int64 // ItemType 为 service 时，这里存储关联的 PetID.

	CurrencyCode string
	Tax          Tax `gorm:"embedded"`

	TipsAmount     decimal.Decimal
	DiscountAmount decimal.Decimal
	SubTotalAmount decimal.Decimal // 等于 UnitPrice * (Quantity - PurchasedQuantity)
	TotalAmount    decimal.Decimal // 等于 SubTotalAmount - DiscountAmount

	RefundedQuantity       int32           // 与 Quantity 对应
	RefundedAmount         decimal.Decimal // 与 TotalAmount 对应
	RefundedTaxAmount      decimal.Decimal // 与 Tax.Amount 对应
	RefundedDiscountAmount decimal.Decimal // 与 DiscountAmount 对应

	IsDeleted  bool
	CreateTime sql.NullTime `gorm:"autoCreateTime"`
	UpdateTime sql.NullTime `gorm:"autoUpdateTime"`

	ItemDiscounts  []*OrderLineDiscount `gorm:"-"`
	DiscountCodeID int64                `gorm:"-"`
}

func (it *OrderItem) AfterFind(_ *gorm.DB) error {
	if it == nil {
		return nil
	}

	it.SetStaff(it.StaffID)

	return nil
}

func (it *OrderItem) AfterCreate(_ *gorm.DB) error {
	if it == nil {
		return nil
	}

	for _, pi := range it.SubTotalItems {
		pi.OrderID = it.OrderID
		pi.OrderItemID = it.ID
	}

	return nil
}

func (it *OrderItem) GetUnitPrice() decimal.Decimal {
	return compatibleWithLegacyZero(it.UnitPrice)
}

func (it *OrderItem) GetTipsAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.TipsAmount)
}

func (it *OrderItem) GetTaxAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.Tax.Amount)
}

func (it *OrderItem) GetDiscountAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.DiscountAmount)
}

func (it *OrderItem) GetSubTotalAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.SubTotalAmount)
}

func (it *OrderItem) GetTotalAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.TotalAmount)
}

func (it *OrderItem) GetRefundedAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.RefundedAmount)
}

func (it *OrderItem) GetRefundedTaxAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.RefundedTaxAmount)
}

func (it *OrderItem) GetRefundedDiscountAmount() decimal.Decimal {
	return compatibleWithLegacyZero(it.RefundedDiscountAmount)
}

func (it *OrderItem) IsProduct() bool {
	if it == nil {
		return false
	}

	return strings.EqualFold(it.ItemType, "product")
}

func (it *OrderItem) IsService() bool {
	if it == nil {
		return false
	}

	return strings.EqualFold(it.ItemType, "service")
}

func (it *OrderItem) IsEvaluation() bool {
	if it == nil {
		return false
	}

	return strings.EqualFold(it.ItemType, "evaluation_service")
}

// GetTotalAmountIncludingTax 返回该 Item 实际支付的含税金额.
// PaidAmount = SubTotalAmount - DiscountAmount + TaxAmount
// 又因为 TotalAmount = SubTotalAmount - DiscountAmount
// 所以这里简化成 PaidAmount = TotalAmount + TaxAmount.
func (it *OrderItem) GetTotalAmountIncludingTax() decimal.Decimal {
	if it == nil || it.IsDeleted {
		return decimal.Zero
	}

	return it.GetTotalAmount().Add(it.Tax.GetAmount())
}

// GetRefundableAmountIncludingTax 返回该 Item 可以退的含税金额.
func (it *OrderItem) GetRefundableAmountIncludingTax() decimal.Decimal {
	if it == nil || it.IsDeleted {
		return decimal.Zero
	}

	return it.GetTotalAmount().Add(it.Tax.GetAmount()).
		Sub(it.GetRefundedAmount()).Sub(it.GetRefundedTaxAmount())
}

func (it *OrderItem) GetRefundableTaxAmount() decimal.Decimal {
	if it == nil || it.IsDeleted {
		return decimal.Zero
	}

	return it.Tax.GetAmount().Sub(it.GetRefundedTaxAmount())
}

func (it *OrderItem) GetRefundableDiscountAmount() decimal.Decimal {
	if it == nil || it.IsDeleted {
		return decimal.Zero
	}

	return it.GetDiscountAmount().Sub(it.GetRefundedDiscountAmount())
}

func (it *OrderItem) SetStaff(staffIDs ...int64) {
	if it == nil {
		return
	}

	staffIDs = lo.Uniq(lo.Filter(staffIDs, func(it int64, _ int) bool { return it > 0 }))

	switch len(staffIDs) {
	case 0: // 清空 Staff 信息
		it.StaffID = 0
		it.StaffIDs = nil

	case 1: // 单个 Staff
		it.StaffID = staffIDs[0]
		it.StaffIDs = staffIDs

	default: // 多 Staff
		it.StaffID = 0
		it.StaffIDs = staffIDs
	}
}

func (it *OrderItem) SetTax(tax Tax) {
	if it == nil {
		return
	}

	it.Tax = tax
}

func (it *OrderItem) ToPB() *orderpb.OrderItemModel {
	return &orderpb.OrderItemModel{
		Id:                it.ID,
		BusinessId:        it.BusinessID,
		ObjectId:          it.ObjectID,
		Type:              it.ItemType,
		Name:              it.Name,
		UnitPrice:         money.FromDecimal(it.UnitPrice, it.CurrencyCode),
		Quantity:          it.Quantity,
		StaffId:           it.StaffID,
		OrderId:           it.OrderID,
		IsDeleted:         it.IsDeleted,
		Description:       it.Description,
		PurchasedQuantity: it.PurchasedQuantity,
		TipsAmount:        money.FromDecimal(it.TipsAmount, it.CurrencyCode),
		TaxAmount:         money.FromDecimal(it.Tax.Amount, it.CurrencyCode),
		DiscountAmount:    money.FromDecimal(it.DiscountAmount, it.CurrencyCode),
		SubTotalAmount:    money.FromDecimal(it.SubTotalAmount, it.CurrencyCode),
		TotalAmount:       money.FromDecimal(it.TotalAmount, it.CurrencyCode),
		CreateTime:        nullTimeToUnix(it.CreateTime),
		UpdateTime:        nullTimeToUnix(it.UpdateTime),
		LineDiscounts: lo.Map(
			it.ItemDiscounts,
			func(old *OrderLineDiscount, _ int) *orderpb.OrderLineDiscountModelV1 {
				return old.ToPB(it.CurrencyCode)
			},
		),
		PetId:                  it.PetID,
		PetDetailId:            0,
		TaxId:                  it.Tax.ID,
		TaxRate:                it.Tax.GetRatePB(),
		TaxName:                it.Tax.Name,
		CurrencyCode:           it.CurrencyCode,
		RefundedQuantity:       it.RefundedQuantity,
		RefundedAmount:         money.FromDecimal(it.RefundedAmount, it.CurrencyCode),
		RefundedTaxAmount:      money.FromDecimal(it.RefundedTaxAmount, it.CurrencyCode),
		RefundedDiscountAmount: money.FromDecimal(it.RefundedDiscountAmount, it.CurrencyCode),
		StaffIds:               it.StaffIDs,
		SubtotalDetail: &orderpb.PriceDetailModel{
			PriceItems: lo.Map(
				it.SubTotalItems,
				func(it *PriceItem, _ int) *orderpb.PriceDetailModel_PriceItem { return it.ToPB() },
			),
		},
		ExternalUuid: it.ExternalUUID,
	}
}

func (it *OrderItem) ToRefundableItem() *ordersvcpb.PreviewRefundOrderResponse_RefundableItem {
	refundableItem := &ordersvcpb.PreviewRefundOrderResponse_RefundableItem{
		OrderItemId:    it.ID,
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		IsRefundable:   true,
		RefundBy: &ordersvcpb.PreviewRefundOrderResponse_RefundableItem_RefundableAmount{
			RefundableAmount: money.FromDecimal(it.TotalAmount.Sub(it.RefundedAmount), it.CurrencyCode),
		},
	}

	if it.IsProduct() {
		refundableItem.RefundItemMode = orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY
		refundableItem.RefundBy = &ordersvcpb.PreviewRefundOrderResponse_RefundableItem_RefundableQuantity{
			RefundableQuantity: it.Quantity - it.RefundedQuantity,
		}
	}

	if it.PurchasedQuantity > 0 {
		refundableItem.IsRefundable = false
	}

	return refundableItem
}

func (it *OrderItem) GetStaffIDs() []int64 {
	if it == nil || len(it.StaffIDs) == 0 {
		return nil
	}

	return it.StaffIDs
}

func (it *OrderItem) Clone() *OrderItem {
	if it == nil {
		return nil
	}

	cp := *it
	cp.StaffIDs = append([]int64{}, it.StaffIDs...)
	cp.ItemDiscounts = lo.Map(
		it.ItemDiscounts,
		func(old *OrderLineDiscount, _ int) *OrderLineDiscount { return old.Clone() },
	)

	return &cp
}

func (it *OrderItem) GetRefundableQuantity() int64 {
	if it == nil {
		return 0
	}

	return int64(it.Quantity - it.RefundedQuantity - it.PurchasedQuantity)
}

func (it *OrderItem) GetRefundableAmount() decimal.Decimal {
	if it == nil {
		return decimal.Zero
	}

	return it.GetTotalAmount().Sub(it.GetRefundedAmount())
}

func (it *OrderItem) GetRefundItemMode() orderpb.RefundItemMode {
	if it == nil {
		return orderpb.RefundItemMode_REFUND_ITEM_MODE_UNSPECIFIED
	}

	if it.IsProduct() || it.Quantity > 1 {
		return orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY
	}

	return orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT
}

func (it *OrderItem) BuildFullyRefundParam() *ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem {
	switch it.GetRefundItemMode() {
	case orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY:
		return &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
			OrderItemId:    it.ID,
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
			RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity{
				RefundQuantity: it.GetRefundableQuantity(),
			},
		}

	case orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT:
		return &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
			OrderItemId:    it.ID,
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
				RefundAmount: money.FromDecimal(
					it.GetRefundableAmount(),
					it.CurrencyCode,
				),
			},
		}

	default:
		return nil
	}
}

func (it *OrderItem) IsAppliedPackage() bool {
	if it == nil {
		return false
	}

	return it.PurchasedQuantity > 0
}

func (it *OrderItem) IsFullyPurchased() bool {
	if it == nil {
		return false
	}

	return it.PurchasedQuantity >= it.Quantity
}

func (it *OrderItem) CannotApplyDiscount() bool {
	if it == nil {
		return true
	}

	return it.GetTotalAmount().IsZero() ||
		it.IsDeleted ||
		it.IsFullyPurchased()
}

func (it *OrderItem) GetItemType() orderpb.ItemType {
	if it == nil {
		return orderpb.ItemType_ITEM_TYPE_UNSPECIFIED
	}

	switch strings.ToLower(it.ItemType) {
	case ItemTypeService:
		return orderpb.ItemType_ITEM_TYPE_SERVICE
	case ItemTypeProduct:
		return orderpb.ItemType_ITEM_TYPE_PRODUCT
	case ItemTypePackage:
		return orderpb.ItemType_ITEM_TYPE_PACKAGE
	case ItemTypeNoShow, "no_show":
		return orderpb.ItemType_ITEM_TYPE_NO_SHOW
	case ItemTypeServiceCharge:
		return orderpb.ItemType_ITEM_TYPE_SERVICE_CHARGE
	case ItemTypeEvaluationService:
		return orderpb.ItemType_ITEM_TYPE_EVALUATION_SERVICE
	case ItemTypeCancellationFee:
		return orderpb.ItemType_ITEM_TYPE_CANCELLATION_FEE
	case ItemTypeMembershipProduct:
		return orderpb.ItemType_ITEM_TYPE_MEMBERSHIP_PRODUCT
	case ItemTypeDeposit:
		return orderpb.ItemType_ITEM_TYPE_DEPOSIT

	default:
		return orderpb.ItemType_ITEM_TYPE_UNSPECIFIED
	}
}
