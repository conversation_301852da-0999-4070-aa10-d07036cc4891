package model

import (
	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
)

type OrderPayment struct {
	ID      int64 `gorm:"primaryKey;autoIncrement"`
	OrderID int64
	// 关联的 Payment 的 ID，进入 TransactionCreated 状态时写入.
	PaymentID int64

	CompanyID  int64
	BusinessID int64
	StaffID    int64
	CustomerID int64

	PaymentMethod PaymentMethod `gorm:"embedded"`
	IsOnline      bool
	IsDeposit     bool
	PaidBy        string

	CurrencyCode string
	// 实际支付的总金额.
	// 发起支付后，如果没有额外增加 Tips，则与 Amount 相等.
	TotalAmount decimal.Decimal
	// 发起支付时的金额，创建时写入，不可更新.
	Amount decimal.Decimal
	// 已退款的金额，取值范围 [0, TotalAmount].
	RefundedAmount decimal.Decimal
	// 渠道费.
	// 支付渠道的供应商收取的渠道费用.
	ProcessingFee decimal.Decimal
	// 使用该支付方式引入的 Convenience Fee.
	// 与 Processing Fee 无逻辑关联.
	// 在发起支付之前计算，在支付成功之后更新到订单.
	ConvenienceFee decimal.Decimal
	// 已退款的 Convenience Fee，取值范围 [0, ConvenienceFee].
	RefundedConvenienceFee decimal.Decimal
	// Customer 给的小费的总金额.
	//   = PaymentTipsBeforeCreate + PaymentTipsAfterCreate
	// 在支付成功之后才会更新到订单上.
	PaymentTips decimal.Decimal
	// 在发起支付前，在我们 MoeGo 侧由 Customer 给的小费，在发起支付之前我们就可以明确知道具体的金额。
	PaymentTipsBeforeCreate decimal.Decimal
	// 在发起支付后，在供应商侧控制和添加的，由 Customer 给的小费（Stripe/Square 的 POS 机），我们无法预先知道，只有在回调中知道具体的值。
	PaymentTipsAfterCreate decimal.Decimal
	// 支付状态.
	PaymentStatus orderpb.OrderPaymentStatus `gorm:"serializer:proto_enum"`
	// 搭配支付状态使用，表示处于当前状态的原因。
	//   - Canceled -> 取消的原因.
	//   - Failed -> 失败的原因.
	PaymentStatusReason string

	CreateTime int64 `gorm:"autoCreateTime;serializer:unixtime"`
	PayTime    int64 `gorm:"serializer:unixtime"`
	FailTime   int64 `gorm:"serializer:unixtime"`
	CancelTime int64 `gorm:"serializer:unixtime"`
	UpdateTime int64 `gorm:"autoUpdateTime;serializer:unixtime"`
}

func (op *OrderPayment) GetRefundableAmount() decimal.Decimal {
	return op.GetTotalAmount().Sub(op.GetRefundedAmount())
}

func (op *OrderPayment) GetRefundableConvenienceFee() decimal.Decimal {
	return op.GetConvenienceFee().Sub(op.GetRefundedConvenienceFee())
}

func (op *OrderPayment) ToRefundablePayment() *ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment {
	return &ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment{
		OrderPaymentId:   op.ID,
		RefundableAmount: money.FromDecimal(op.GetRefundableAmount(), op.CurrencyCode),
	}
}

func (op *OrderPayment) IsPaid() bool {
	return op.PaymentStatus == orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID
}

func (op *OrderPayment) IsFailed() bool {
	return op.PaymentStatus == orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_FAILED
}

func (op *OrderPayment) IsCanceled() bool {
	return op.PaymentStatus == orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CANCELED
}

func (op *OrderPayment) IsFinalState() bool {
	return op.IsPaid() || op.IsFailed() || op.IsCanceled()
}

func (op *OrderPayment) Clone() *OrderPayment {
	cp := *op
	return &cp
}

func (op *OrderPayment) ToPB() *orderpb.OrderPaymentModel {
	return &orderpb.OrderPaymentModel{
		Id:                            op.ID,
		OrderId:                       op.OrderID,
		PaymentId:                     op.PaymentID,
		CompanyId:                     op.CompanyID,
		BusinessId:                    op.BusinessID,
		CustomerId:                    op.CustomerID,
		StaffId:                       op.StaffID,
		PaymentMethodId:               op.PaymentMethod.ID,
		PaymentMethod:                 op.PaymentMethod.Method,
		PaymentMethodDisplayName:      op.PaymentMethod.GetDisplayName(),
		PaymentMethodExtra:            op.PaymentMethod.Extra.ToPB(),
		PaymentMethodVendor:           op.PaymentMethod.Vendor,
		IsOnline:                      op.IsOnline,
		IsDeposit:                     op.IsDeposit,
		PaidBy:                        op.PaidBy,
		CurrencyCode:                  op.CurrencyCode,
		TotalAmount:                   money.FromDecimal(op.TotalAmount, op.CurrencyCode),
		Amount:                        money.FromDecimal(op.Amount, op.CurrencyCode),
		RefundedAmount:                money.FromDecimal(op.RefundedAmount, op.CurrencyCode),
		ProcessingFee:                 money.FromDecimal(op.ProcessingFee, op.CurrencyCode),
		PaymentConvenienceFee:         money.FromDecimal(op.ConvenienceFee, op.CurrencyCode),
		RefundedPaymentConvenienceFee: money.FromDecimal(op.RefundedConvenienceFee, op.CurrencyCode),
		PaymentTips:                   money.FromDecimal(op.PaymentTips, op.CurrencyCode),
		PaymentTipsBeforeCreate:       money.FromDecimal(op.PaymentTipsBeforeCreate, op.CurrencyCode),
		PaymentTipsAfterCreate:        money.FromDecimal(op.PaymentTipsAfterCreate, op.CurrencyCode),
		PaymentStatus:                 op.PaymentStatus,
		PaymentStatusReason:           op.PaymentStatusReason,
		CreateTime:                    op.CreateTime,
		PayTime:                       op.PayTime,
		FailTime:                      op.FailTime,
		CancelTime:                    op.CancelTime,
		UpdateTime:                    op.UpdateTime,
	}
}

func (op *OrderPayment) GetTotalAmount() decimal.Decimal {
	return compatibleWithLegacyZero(op.TotalAmount)
}

func (op *OrderPayment) GetAmount() decimal.Decimal {
	return compatibleWithLegacyZero(op.Amount)
}

func (op *OrderPayment) GetRefundedAmount() decimal.Decimal {
	return compatibleWithLegacyZero(op.RefundedAmount)
}

func (op *OrderPayment) GetProcessingFee() decimal.Decimal {
	return compatibleWithLegacyZero(op.ProcessingFee)
}

func (op *OrderPayment) GetConvenienceFee() decimal.Decimal {
	return compatibleWithLegacyZero(op.ConvenienceFee)
}

func (op *OrderPayment) GetRefundedConvenienceFee() decimal.Decimal {
	return compatibleWithLegacyZero(op.RefundedConvenienceFee)
}

func (op *OrderPayment) GetPaymentTips() decimal.Decimal {
	return compatibleWithLegacyZero(op.PaymentTips)
}

func (op *OrderPayment) GetPaymentTipsBeforeCreate() decimal.Decimal {
	return compatibleWithLegacyZero(op.PaymentTipsBeforeCreate)
}

func (op *OrderPayment) GetPaymentTipsAfterCreate() decimal.Decimal {
	return compatibleWithLegacyZero(op.PaymentTipsAfterCreate)
}
