package model

import (
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

type RefundOrderItem struct {
	ID            int64 `gorm:"primaryKey;autoIncrement"`
	OrderID       int64
	OrderItemID   int64
	RefundOrderID int64

	CompanyID  int64
	BusinessID int64
	CustomerID int64
	StaffID    int64
	StaffIDs   []int64 `gorm:"-"` // 从对应的 OrderItem 那边整来的.
	PetID      int64   `gorm:"-"` // 从对应的 OrderItem 那边整来的.

	ItemType        string
	ItemID          int64
	ItemName        string
	ItemDescription string
	ItemUnitPrice   decimal.Decimal

	CurrencyCode   string
	RefundItemMode orderpb.RefundItemMode `gorm:"serializer:proto_enum"`
	RefundTax      RefundTax              `gorm:"embedded"`
	// 总退款金额 = (ItemUnitPrice * RefundQuantity)
	//            + RefundAmount
	//            - RefundDiscountAmount
	// 特别的，这里为了对齐正向 OrderItem 的字段，把税从 TotalAmount 中移除了.
	RefundTotalAmount decimal.Decimal
	// RefundItemMode 为 ByQuantity 时有值, 否则为 0.
	RefundQuantity       int32
	RefundAmount         decimal.Decimal
	RefundDiscountAmount decimal.Decimal

	CreateTime int64 `gorm:"autoCreateTime;serializer:unixtime"`
}

func (roi *RefundOrderItem) AfterFind(_ *gorm.DB) error {
	if roi == nil {
		return nil
	}

	roi.SetStaff(roi.StaffID)

	return nil
}

func (roi *RefundOrderItem) SetStaff(staffIDs ...int64) {
	if roi == nil {
		return
	}

	staffIDs = lo.Uniq(lo.Filter(staffIDs, func(it int64, _ int) bool { return it > 0 }))

	switch len(staffIDs) {
	case 0: // 清空 Staff 信息
		roi.StaffID = 0
		roi.StaffIDs = nil

	case 1: // 单个 Staff
		roi.StaffID = staffIDs[0]
		roi.StaffIDs = staffIDs

	default: // 多 Staff
		roi.StaffID = 0
		roi.StaffIDs = staffIDs
	}
}

// AttachOrderItem 从对应的 OrderItem 获取部分缺失的 RefundOrderItem 信息.
func (roi *RefundOrderItem) AttachOrderItem(oi *OrderItem) {
	if roi == nil || oi == nil {
		return
	}

	if roi.OrderItemID != oi.ID {
		return
	}

	roi.SetStaff(oi.GetStaffIDs()...)
	roi.PetID = oi.PetID
}

func (roi *RefundOrderItem) GetItemUnitPrice() decimal.Decimal {
	return compatibleWithLegacyZero(roi.ItemUnitPrice)
}

func (roi *RefundOrderItem) GetRefundTotalAmount() decimal.Decimal {
	return compatibleWithLegacyZero(roi.RefundTotalAmount)
}

func (roi *RefundOrderItem) GetRefundAmount() decimal.Decimal {
	return compatibleWithLegacyZero(roi.RefundAmount)
}

func (roi *RefundOrderItem) GetRefundDiscountAmount() decimal.Decimal {
	return compatibleWithLegacyZero(roi.RefundDiscountAmount)
}

func (roi *RefundOrderItem) ToPB() *orderpb.RefundOrderItemModel {
	return &orderpb.RefundOrderItemModel{
		Id:                  roi.ID,
		OrderId:             roi.OrderID,
		OrderItemId:         roi.OrderItemID,
		RefundOrderId:       roi.RefundOrderID,
		CompanyId:           roi.CompanyID,
		BusinessId:          roi.BusinessID,
		CustomerId:          roi.CustomerID,
		StaffId:             roi.StaffID,
		StaffIds:            roi.StaffIDs,
		ItemId:              roi.ItemID,
		ItemType:            roi.ItemType,
		ItemName:            roi.ItemName,
		ItemDescription:     roi.ItemDescription,
		ItemUnitPrice:       money.FromDecimal(roi.ItemUnitPrice, roi.CurrencyCode),
		TaxId:               roi.RefundTax.ID,
		TaxRate:             roi.RefundTax.GetRatePB(),
		TaxName:             roi.RefundTax.Name,
		PetId:               roi.PetID,
		RefundOrderItemMode: roi.RefundItemMode,
		CurrencyCode:        roi.CurrencyCode,
		RefundTotalAmount:   money.FromDecimal(roi.RefundTotalAmount, roi.CurrencyCode),
		RefundQuantity:      int64(roi.RefundQuantity),
		RefundAmount:        money.FromDecimal(roi.RefundAmount, roi.CurrencyCode),
		RefundDiscount:      money.FromDecimal(roi.RefundDiscountAmount, roi.CurrencyCode),
		RefundTax:           money.FromDecimal(roi.RefundTax.Amount, roi.CurrencyCode),
		CreateTime:          roi.CreateTime,
	}
}
