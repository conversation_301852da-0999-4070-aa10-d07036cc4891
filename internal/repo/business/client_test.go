package business

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/suite"

	mhttp "github.com/MoeGolibrary/go-lib/http"
)

type BusinessClientTestSuite struct {
	suite.Suite

	svr     *httptest.Server
	clit    Client
	handler http.HandlerFunc
}

func TestBusinessClient(t *testing.T) {
	suite.Run(t, new(BusinessClientTestSuite))
}

func (ts *BusinessClientTestSuite) TestGetBusinessPayrollSettingByBid() {
	called := false
	expected := &PayrollSetting{
		ID:              100,
		BusinessID:      200,
		CompanyID:       300,
		SplitTipsMethod: 2,
	}

	ts.handler = func(w http.ResponseWriter, r *http.Request) {
		called = true

		ts.Equal(http.MethodGet, r.Method)
		w.Write(
			[]byte(`{
				"id": 100,
				"businessId": 200,
				"companyId": 300,
				"splitTipsMethod": 2
			}`),
		)
	}

	resp, err := ts.clit.GetPayrollSetting(context.Background(), 100)
	ts.NoError(err)
	ts.Equal(expected, resp)
	ts.True(called)
}

// Add these setup methods to your test suite
func (ts *BusinessClientTestSuite) SetupSuite() {
	ts.svr = httptest.NewServer(
		http.HandlerFunc(
			func(w http.ResponseWriter, r *http.Request) {
				if ts.handler == nil {
					w.WriteHeader(http.StatusNotImplemented)
					return
				}

				ts.handler.ServeHTTP(w, r)
			},
		),
	)

	ts.clit = NewBusinessClient()
	ts.clit.(*client).cli = mhttp.NewClient(ts.svr.URL)
}

func (ts *BusinessClientTestSuite) TearDownSuite() {
	ts.svr.Close()
}

func (ts *BusinessClientTestSuite) SetupTest() {
	ts.handler = nil
}
