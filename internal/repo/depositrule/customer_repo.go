package depositrule

import (
	"bytes"
	"context"
	"encoding/json"
	"net/url"

	mhttp "github.com/MoeGolibrary/go-lib/http"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/httphelper"
)

type CustomerRepo interface {
	// SmartListCountByFilter returns the count of customers with the given request. We doesn't need the filtered
	// customer detail, so we ignore it for simplicity.
	SmartListCountByFilter(ctx context.Context, req *SmartListByFilterRequest) (int, error)
}

func NewCustomerRepo() CustomerRepo {
	return &customerRepo{
		client: mhttp.NewClient("http://moego-service-customer:9201"),
	}
}

type customerRepo struct {
	client mhttp.Client
}

type SmartListByFilterRequest struct {
	CompanyID        int64           `json:"companyId"`         //nolint:tagliatelle // Java server API
	BusinessID       int64           `json:"businessId"`        //nolint:tagliatelle // Java server API
	StaffID          *int64          `json:"staffId,omitempty"` //nolint:tagliatelle // Java server API
	ClientListParams SmartListParams `json:"clientListParams"`
}

// SmartListParams
// 对应的结构是 moego-server-api 的 com.moego.server.customer.params.ClientListParams 类，虽然 filters 之外的其他字段都标记为
// NotNull 了，但是实际发现可以为空……所以这里也直接不定义了。
type SmartListParams struct {
	// json tag 的确为 filters，但是 SmartListFilter 是一个递归定义的过滤器，所以字段名使用了单数。
	Filter SmartListFilter `json:"filters"` //nolint:tagliatelle // Java server API
}

type SmartListFilter struct {
	Type     string            `json:"type,omitempty"`     // corresponds to TypeEnum
	Filters  []SmartListFilter `json:"filters,omitempty"`  // group of filters
	Operator string            `json:"operator,omitempty"` // corresponds to OperatorEnum
	Property string            `json:"property,omitempty"` // corresponds to PropertyEnum
	Value    any               `json:"value,omitempty"`    // single value
	Values   []any             `json:"values,omitempty"`   // multi value
}

func (f *SmartListFilter) IsEmpty() bool {
	return f.Type == "" && len(f.Filters) == 0 && f.Operator == ""
}

type SmartListQueries struct {
	Keyword string `json:"keyword"`
}

type SmartListSort struct {
	Property string `json:"property"`
	// asc, desc
	Order string `json:"order"`
}

func (repo *customerRepo) SmartListCountByFilter(ctx context.Context, req *SmartListByFilterRequest) (int, error) {
	const path = "/service/customer/customer/getSmartClientListV2"

	reqBody, err := json.Marshal(req)
	if err != nil {
		return 0, err
	}

	resp, err := repo.client.Post(ctx, path, url.Values{}, bytes.NewReader(reqBody))
	if err != nil {
		return 0, err
	}

	defer func() { _ = resp.Body.Close() }()

	type respBodyStruct struct {
		CustomerTotal int `json:"customerTotal"`
	}

	respBody, err := httphelper.ParseResponse[respBodyStruct](resp)
	if err != nil {
		return 0, err
	}

	return respBody.CustomerTotal, nil
}
