package depositrule

import (
	"context"

	"github.com/MoeGolibrary/go-lib/grpc"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
)

type OrganizationRepo interface {
	GetCompanyPreference(ctx context.Context, companyID int64) (*organizationpb.CompanyPreferenceSettingModel, error)
}

func NewBusinessRepo() OrganizationRepo {
	return &businessRepo{
		companyClient: grpc.NewClient("moego-svc-organization:9090", organizationsvcpb.NewCompanyServiceClient),
	}
}

type businessRepo struct {
	companyClient organizationsvcpb.CompanyServiceClient
}

func (repo *businessRepo) GetCompanyPreference(
	ctx context.Context, companyID int64,
) (*organizationpb.CompanyPreferenceSettingModel, error) {
	resp, err := repo.companyClient.GetCompanyPreferenceSetting(ctx, &organizationsvcpb.GetCompanyPreferenceSettingRequest{
		CompanyId: companyID,
	})
	if err != nil {
		return nil, err
	}

	return resp.GetPreferenceSetting(), nil
}
