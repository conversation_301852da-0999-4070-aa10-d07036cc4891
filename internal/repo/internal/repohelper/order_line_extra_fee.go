package repohelper

import (
	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type CollectType string

const (
	CollectTypeAdd      CollectType = "add"
	CollectTypeSubtract CollectType = "subtract"
)

type OrderLineExtraFee struct {
	ID            int64 `gorm:"primaryKey"`
	BusinessID    int64
	OrderID       int64
	OrderItemID   int64
	ApplyType     string
	IsDeleted     bool
	FeeType       string
	Amount        decimal.Decimal
	Name          string
	Description   string
	CollectType   CollectType
	ApplyBy       int64
	ApplySequence int64
	CreateTime    int64 `gorm:"autoCreateTime;serializer:unixtime"`
	UpdateTime    int64 `gorm:"autoUpdateTime;serializer:unixtime"`
}

// ApplyRefundOrder 用于退款时从订单上减去已退的 Convenience Fee.
func (ole *OrderLineExtraFee) ApplyRefundOrder(rod *model.RefundOrder) {
	if ole == nil || rod == nil {
		return
	}

	ole.ID = 0
	ole.BusinessID = rod.BusinessID
	ole.OrderID = rod.OrderID
	ole.OrderItemID = 0    // 不适用
	ole.ApplyType = "none" // 固定值
	ole.IsDeleted = false
	ole.FeeType = "convenience fee" // 固定值
	ole.Amount = rod.GetRefundConvenienceFee()
	ole.Name = ""        // 不适用
	ole.Description = "" // 不适用
	ole.CollectType = CollectTypeSubtract
	ole.ApplyBy = 0       // 固定值
	ole.ApplySequence = 0 // 固定值
}
