package repohelper

import (
	"strconv"

	"github.com/shopspring/decimal"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

// PaymentModel 与 PaymentDB 中的 payment 表完全一致（包括名字和顺序）.
// 仅用于辅助构造老订单数据.
type PaymentModel struct {
	ID                  int64 `gorm:"primaryKey"`
	Module              string
	InvoiceID           int64
	CustomerID          int64
	StaffID             int64
	Method              string
	Amount              decimal.Decimal
	Status              PaymentStatus
	CreateTime          int64
	UpdateTime          int64
	CheckNumber         string
	CardType            string
	CardNumber          string
	ExpMonth            string
	ExpYear             string
	Signature           string
	PaidBy              string
	Description         string
	MethodID            int64
	StripeIntentID      string
	StripeClientSecret  string
	BusinessID          int64
	StripeChargeID      string
	IsOnline            []uint8
	Currency            string
	GroomingID          int64
	SquareCustomerID    string
	LocationID          string
	SquarePaymentMethod int32
	DeviceID            string
	SquareCheckoutID    string
	Vendor              string
	Merchant            string
	CancelReason        string
	Tips                decimal.Decimal
	ProcessingFee       decimal.Decimal
	IsDeposit           int32
	StripePaymentMethod int32
	CardFunding         string
	CompanyID           int64
}

func (pm *PaymentModel) getExpMonth() int32 {
	month, _ := strconv.ParseInt(pm.ExpMonth, 10, 32)
	return int32(month)
}

func (pm *PaymentModel) getExpYear() int32 {
	year, _ := strconv.ParseInt(pm.ExpYear, 10, 32)
	return int32(year)
}

func (pm *PaymentModel) ToOrderPayment() *model.OrderPayment {
	op := &model.OrderPayment{
		ID:         pm.ID,
		OrderID:    pm.InvoiceID,
		PaymentID:  pm.ID, // 就是 Payment 本身.
		CompanyID:  pm.CompanyID,
		BusinessID: pm.BusinessID,
		StaffID:    pm.StaffID,
		CustomerID: pm.CustomerID,
		PaymentMethod: model.PaymentMethod{
			ID:     pm.MethodID,
			Method: pm.Method,
			Extra: model.PaymentMethodExtra{
				CardFunding:         pm.CardFunding,
				CardType:            pm.CardType,
				CardNumber:          pm.CardNumber,
				CheckNumber:         pm.CheckNumber,
				DeviceID:            pm.DeviceID,
				ExpMonth:            pm.getExpMonth(),
				ExpYear:             pm.getExpYear(),
				Merchant:            pm.Merchant,
				Signature:           pm.Signature,
				StripeClientSecret:  pm.StripeClientSecret,
				StripeChargeID:      pm.StripeChargeID,
				StripeIntentID:      pm.StripeIntentID,
				StripePaymentMethod: model.StripePaymentMethod(pm.StripePaymentMethod),
				SquareCheckoutID:    pm.SquareCheckoutID,
				SquareCustomerID:    pm.SquareCustomerID,
				SquarePaymentMethod: model.SquarePaymentMethod(pm.SquarePaymentMethod),
			},
			Vendor: pm.Vendor,
		},
		IsOnline:                len(pm.IsOnline) > 0 && pm.IsOnline[0] == 1,
		IsDeposit:               pm.IsDeposit != 0,
		PaidBy:                  pm.PaidBy,
		CurrencyCode:            pm.Currency,
		TotalAmount:             pm.Amount,
		Amount:                  pm.Amount,
		RefundedAmount:          decimal.Decimal{}, // 无该字段
		ProcessingFee:           pm.ProcessingFee,
		ConvenienceFee:          decimal.Decimal{}, // 无该字段
		RefundedConvenienceFee:  decimal.Decimal{}, // 无该字段
		PaymentTips:             pm.Tips,
		PaymentTipsBeforeCreate: decimal.Decimal{}, // 无法区分
		PaymentTipsAfterCreate:  decimal.Decimal{}, // 无法区分
		PaymentStatus:           pm.Status.ToOrderPaymentStatus(),
		PaymentStatusReason:     "", // Fill below.
		CreateTime:              pm.CreateTime,
		PayTime:                 0, // Fill below.
		FailTime:                0, // Fill below.
		CancelTime:              0, // 无该字段.
		UpdateTime:              pm.UpdateTime,
	}

	switch op.PaymentStatus {
	case orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID:
		// Payment 无 PayTime 记录，用 Update Time 来顶替一下
		op.PayTime = pm.UpdateTime

		return op

	case orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_FAILED:
		// Payment 无 FailTime 记录，用 Update Time 来顶替一下
		op.FailTime = pm.UpdateTime
		// 无对应字段.
		op.PaymentStatusReason = pm.CancelReason

		return op

	default:
		return op
	}
}
