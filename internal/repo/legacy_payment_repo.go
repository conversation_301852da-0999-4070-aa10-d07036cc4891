package repo

import (
	"context"

	"github.com/samber/lo"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

// PaymentDB 对于老订单而言， Payment DB 中的 Payment 就是 Order Payment.
// 因此这里实现一套读的逻辑用来适配老订单.
// 后续无老订单之后移除.
type PaymentDB gorm.DB

type LegacyPaymentRepo interface {
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error)
}

type legacyPaymentRepo struct {
	db *gorm.DB
}

func NewLegacyPaymentRepo(db *PaymentDB) LegacyPaymentRepo {
	return &legacyPaymentRepo{db: (*gorm.DB)(db)}
}

func (repo *legacyPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error) {
	payments, err := listByKeyID[*repohelper.PaymentModel](repo.withContext(ctx), "invoice_id", orderID)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.PaymentModel, _ int) *model.OrderPayment { return p.ToOrderPayment() },
	), nil
}

func (repo *legacyPaymentRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "payment"
	return repo.db.WithContext(ctx).Table(tableName)
}
