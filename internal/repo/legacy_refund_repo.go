package repo

import (
	"context"

	"github.com/samber/lo"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type LegacyRefundRepo interface {
	ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error)
}

type legacyRefundRepo struct {
	db *gorm.DB
}

func NewLegacyRefundRepo(db *PaymentDB) LegacyRefundRepo {
	return &legacyRefundRepo{db: (*gorm.DB)(db)}
}

func (repo *legacyRefundRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error) {
	payments, err := listByKeyID[*repohelper.RefundPaymentModel](repo.withContext(ctx), "invoice_id", orderID)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		payments,
		func(p *repohelper.RefundPaymentModel, _ int) *model.RefundOrderPayment {
			return p.ToRefundOrderPayment()
		},
	), nil
}

func (repo *legacyRefundRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "refund"
	return repo.db.WithContext(ctx).Table(tableName)
}
