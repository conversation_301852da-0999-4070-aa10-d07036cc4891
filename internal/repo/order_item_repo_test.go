package repo

import (
	"context"
	"database/sql"
	"regexp"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/gorm"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderItemTestSuite struct {
	suite.Suite

	mockDB  *gorm.DB
	sqlMock sqlmock.Sqlmock
	repo    OrderItemRepo
}

func TestOrderItemRepo(t *testing.T) {
	suite.Run(t, new(OrderItemTestSuite))
}

func (ts *OrderItemTestSuite) SetupTest() {
	ts.mockDB, ts.sqlMock = gorm.MockPostgres(ts.T())
	ts.repo = NewOrderItemRepo(ts.mockDB)
}

func (ts *OrderItemTestSuite) TestGet() {
	timestamp, err := time.Parse("2006-01-02 15:04:05.999999 -07:00", "2024-10-08 03:56:57.028000 +00:00")
	ts.Require().NoError(err)

	expectedOrderItem := &model.OrderItem{
		ID:                *********,
		OrderID:           *********,
		BusinessID:        101823,
		StaffID:           1,
		StaffIDs:          []int64{1}, // StaffID 非 0 时，需要包含在 StaffIDs 中.
		ItemType:          "service",
		ObjectID:          1023347,
		Name:              "Nail Trim (NT) (import)",
		Description:       "NT",
		UnitPrice:         mustParseDecimal("1.11"),
		Quantity:          2,
		PurchasedQuantity: 3,
		PetID:             4,
		CurrencyCode:      "USD",
		Tax: model.Tax{
			ID:     5,
			Name:   "tax name",
			Rate:   mustParseDecimal("0.22"),
			Amount: mustParseDecimal("3.33"), // 与外面的 TaxAmount 相同
		},
		TipsAmount:             mustParseDecimal("2.22"),
		DiscountAmount:         mustParseDecimal("4.44"),
		SubTotalAmount:         mustParseDecimal("6.66"),
		TotalAmount:            mustParseDecimal("7.77"),
		RefundedQuantity:       9,
		RefundedAmount:         mustParseDecimal("10.11"),
		RefundedTaxAmount:      mustParseDecimal("11.11"),
		RefundedDiscountAmount: mustParseDecimal("12.22"),
		IsDeleted:              false,
		CreateTime:             sql.NullTime{Time: timestamp, Valid: true},
		UpdateTime:             sql.NullTime{Time: timestamp, Valid: true},
	}

	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order_line_item" WHERE id = $1 LIMIT $2`)).WithArgs(
		*********, 1,
	).WillReturnRows(
		sqlmock.NewRows(
			[]string{
				"id", "business_id", "staff_id", "order_id", "object_id", "type", "is_deleted", "name", "description",
				"unit_price", "quantity", "purchased_quantity",
				"tips_amount", "tax_amount", "discount_amount", "extra_fee_amount", "sub_total_amount", "total_amount",
				"create_time", "update_time",
				"pet_id", "currency_code",
				"refunded_quantity", "refunded_amount", "refunded_tax_amount", "refunded_discount_amount",
				"refunded_convenience_fee_amount",
				"tax_id", "tax_name", "tax_rate",
			},
		).AddRow(
			*********, 101823, 1, *********, 1023347, "service", false, "Nail Trim (NT) (import)", "NT",
			1.11, 2, 3,
			2.22, 3.33, 4.44, 5.55, 6.66, 7.77,
			timestamp, timestamp,
			4, "USD",
			9, 10.11, 11.11, 12.22,
			13.33,
			5, "tax name", 0.22,
		),
	)

	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order_item_price_detail" WHERE order_id = $1 ORDER BY "order_item_price_detail"."id" LIMIT $2`)).WithArgs(
		*********, 50,
	).WillReturnRows(sqlmock.NewRows([]string{}))

	orderItem, err := ts.repo.Get(context.Background(), *********)
	ts.Require().NoError(err)
	ts.Require().NotNil(orderItem)
	ts.Equal(expectedOrderItem, orderItem)
}
