package repo

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderPaymentRepo interface {
	Get(ctx context.Context, id int64) (*model.OrderPayment, error)
	BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error)

	ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error)

	UpdateRefund(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) (int64, error)
	CancelOrderPayment(ctx context.Context, orderPayment *model.OrderPayment) (int64, error)
}

type orderPaymentRepo struct {
	db *gorm.DB
}

func NewOrderPaymentRepo(db *gorm.DB) OrderPaymentRepo {
	return &orderPaymentRepo{db: db}
}

func (repo *orderPaymentRepo) Get(ctx context.Context, id int64) (*model.OrderPayment, error) {
	return getByID[*model.OrderPayment](repo.withContext(ctx), id)
}

func (repo *orderPaymentRepo) BatchGet(ctx context.Context, ids []int64) ([]*model.OrderPayment, error) {
	return listByConditions[*model.OrderPayment](
		repo.withContext(ctx),
		map[string][]any{"id IN (?)": {ids}},
	)
}

func (repo *orderPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.OrderPayment, error) {
	return listByKeyID[*model.OrderPayment](repo.withContext(ctx), "order_id", orderID)
}

func (repo *orderPaymentRepo) UpdateRefund(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) (
	int64, error,
) {
	tx := repo.withContext(ctx).Where("id = ?", refundOrderPayment.OrderPaymentID).
		Where("total_amount >= ?", gorm.Expr("refunded_amount + ?", refundOrderPayment.RefundAmount)).
		Where(
			"convenience_fee >= ?", gorm.Expr(
				"refunded_convenience_fee + ?", refundOrderPayment.RefundConvenienceFee,
			),
		).
		Updates(
			map[string]any{
				"refunded_amount": gorm.Expr("refunded_amount + ?", refundOrderPayment.RefundAmount),
				"refunded_convenience_fee": gorm.Expr(
					"refunded_convenience_fee + ?", refundOrderPayment.RefundConvenienceFee,
				),
				"update_time": toTimestamp(time.Now()),
			},
		)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot update order payment for refund")
	}

	return tx.RowsAffected, tx.Error
}

func (repo *orderPaymentRepo) CancelOrderPayment(ctx context.Context, orderPayment *model.OrderPayment) (int64, error) {
	tx := repo.withContext(ctx).Where("id = ?", orderPayment.ID).
		Where(
			"payment_status IN ?", []string{
				orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CREATED.String(),
				orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
			},
		).
		Updates(
			map[string]any{
				"payment_status": orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CANCELED,
				"reason":         orderPayment.PaymentStatusReason,
				"cancel_time":    toTimestamp(time.Now()),
				"update_time":    toTimestamp(time.Now()),
			},
		)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot cancel order payment")
	}

	return tx.RowsAffected, tx.Error
}

func (repo *orderPaymentRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order_payment"

	return repo.db.WithContext(ctx).Table(tableName)
}
