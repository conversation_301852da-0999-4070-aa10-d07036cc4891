package repo

import (
	"context"
	"regexp"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/gorm"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type OrderPaymentTestSuite struct {
	suite.Suite

	mockDB  *gorm.DB
	sqlMock sqlmock.Sqlmock

	repo OrderPaymentRepo
}

func TestOrderPayment(t *testing.T) {
	suite.Run(t, new(OrderPaymentTestSuite))
}

func (ts *OrderPaymentTestSuite) SetupTest() {
	ts.mockDB, ts.sqlMock = gorm.MockPostgres(ts.T())
	ts.repo = NewOrderPaymentRepo(ts.mockDB)
}

func (ts *OrderPaymentTestSuite) TestGet() {
	timestamp := time.Now()

	// 为了验证 ORM 构造的数据，金额不是按照实际规则构建的。
	expectedOrderPayment := &model.OrderPayment{
		ID:         1,
		OrderID:    2,
		PaymentID:  10,
		CompanyID:  3,
		BusinessID: 4,
		StaffID:    5,
		CustomerID: 6,
		PaymentMethod: model.PaymentMethod{
			ID:     7,
			Method: "payment method name",
			Extra: model.PaymentMethodExtra{
				CardFunding:         "card funding",
				CardType:            "card type",
				CardNumber:          "card number",
				CheckNumber:         "check number",
				DeviceID:            "device id",
				ExpMonth:            1,
				ExpYear:             2222,
				Merchant:            "merchant",
				Signature:           "signature",
				StripeClientSecret:  "stripe client secret",
				StripeChargeID:      "stripe charge id",
				StripeIntentID:      "stripe intent id",
				StripePaymentMethod: 3,
				SquareCheckoutID:    "square checkout id",
				SquareCustomerID:    "square customer id",
				SquarePaymentMethod: 4,
			},
			Vendor: "payment method vendor",
		},
		IsOnline:                true,
		IsDeposit:               true,
		PaidBy:                  "paid by",
		CurrencyCode:            "USD",
		TotalAmount:             mustParseDecimal("1.11"),
		Amount:                  mustParseDecimal("2.22"),
		RefundedAmount:          mustParseDecimal("3.33"),
		ProcessingFee:           mustParseDecimal("4.44"),
		ConvenienceFee:          mustParseDecimal("8.88"),
		RefundedConvenienceFee:  mustParseDecimal("9.99"),
		PaymentTips:             mustParseDecimal("5.55"),
		PaymentTipsBeforeCreate: mustParseDecimal("6.66"),
		PaymentTipsAfterCreate:  mustParseDecimal("7.77"),
		PaymentStatus:           orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
		PaymentStatusReason:     "payment status reason",
		CreateTime:              timestamp.Unix(),
		PayTime:                 timestamp.Unix() + 1,
		FailTime:                timestamp.Unix() + 2,
		CancelTime:              timestamp.Unix() + 3,
		UpdateTime:              timestamp.Unix() + 4,
	}

	rawPaymentExtra := []byte(`{
	"cardFunding": "card funding",
	"cardType": "card type",
	"cardNumber": "card number",
	"checkNumber": "check number",
	"deviceID": "device id",
	"expMonth": 1,
	"expYear": 2222,
	"merchant": "merchant",
	"signature": "signature",
	"stripeClientSecret": "stripe client secret",
	"stripeChargeID": "stripe charge id",
	"stripeIntentID": "stripe intent id",
	"stripePaymentMethod": 3,
	"squareCheckoutID": "square checkout id",
	"squareCustomerID": "square customer id",
	"squarePaymentMethod": 4
}`)

	ts.sqlMock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "public"."order_payment" WHERE id = $1 LIMIT $2`)).WithArgs(
		1, 1,
	).WillReturnRows(
		sqlmock.NewRows(
			[]string{
				"id", "order_id", "payment_id", "company_id", "business_id", "staff_id", "customer_id",
				"payment_method_id", "payment_method", "payment_method_extra", "payment_method_vendor",
				"is_online", "is_deposit", "paid_by",
				"currency_code", "total_amount", "amount", "refunded_amount", "processing_fee",
				"convenience_fee", "refunded_convenience_fee",
				"payment_tips", "payment_tips_before_create", "payment_tips_after_create",
				"payment_status", "payment_status_reason",
				"create_time", "pay_time", "fail_time",
				"cancel_time", "update_time",
			},
		).AddRow(
			1, 2, 10, 3, 4, 5, 6,
			7, "payment method name", rawPaymentExtra, "payment method vendor",
			true, true, "paid by",
			"USD", 1.11, 2.22, 3.33, 4.44,
			8.88, 9.99,
			5.55, 6.66, 7.77,
			"ORDER_PAYMENT_STATUS_PAID", "payment status reason",
			timestamp, timestamp.Add(1*time.Second), timestamp.Add(2*time.Second),
			timestamp.Add(3*time.Second), timestamp.Add(4*time.Second),
		),
	)

	orderPayment, err := ts.repo.Get(context.Background(), 1)
	ts.Require().NoError(err)
	ts.Require().NotNil(orderPayment)
	ts.Equal(expectedOrderPayment, orderPayment)
}
