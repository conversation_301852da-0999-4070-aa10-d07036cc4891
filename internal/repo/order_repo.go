package repo

import (
	"context"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type OrderRepo interface {
	Create(ctx context.Context, order *model.Order) error

	Get(ctx context.Context, id int64) (*model.Order, error)
	GetRootBySource(
		ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64,
	) (*model.Order, error)
	BatchGetOrders(
		ctx context.Context,
		orderIDs []int64,
	) ([]*model.Order, error)
	ListBySource(
		ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
	) ([]*model.Order, error)
	ListBySourceForUpdate(
		ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
	) ([]*model.Order, error)
	// ListTailOrder lists all orders whose order_ref_id is the given order ID (which is, "head order").
	ListTailOrder(ctx context.Context, originID int64) ([]*model.Order, error)
	ListByAppointment(ctx context.Context, appointmentID int64) ([]*model.Order, error)

	ListByFulfillment(ctx context.Context, fulfillmentID int64) ([]*model.Order, error)

	GetForUpdate(ctx context.Context, id int64) (*model.Order, error)
	ListByAppointmentForUpdate(ctx context.Context, appointmentID int64) ([]*model.Order, error)
	UpdateRefund(ctx context.Context, refundOrder *model.RefundOrder) (int64, error)
	ResetTipAmount(ctx context.Context, order *model.Order) (int64, error)
	CancelOrder(ctx context.Context, order *model.Order) (int64, error)
	UpdateSourceForType(
		ctx context.Context, orderType orderpb.OrderModel_OrderType,
		oldSourceType orderpb.OrderSourceType, oldSourceID int64,
		newSourceType orderpb.OrderSourceType, newSourceID int64,
	) error
}

func NewOrderRepo(db *gorm.DB) OrderRepo {
	return &orderRepo{
		db:                db,
		orderLineExtraFee: newOrderLineExtraFeeRepo(db),
	}
}

type orderRepo struct {
	db *gorm.DB

	orderLineExtraFee *orderLineExtraFeeRepo
}

// ResetTipAmount implements OrderRepo.
func (repo *orderRepo) ResetTipAmount(ctx context.Context, order *model.Order) (int64, error) {
	tx := repo.withContext(ctx).
		Select("tips_amount", "paid_amount", "remain_amount", "total_amount", "staff_id", "description", "update_time").
		Where("id = ?", order.ID).
		Updates(order)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot update tip order")
	}

	return tx.RowsAffected, nil
}

// ListBySourceTypeAndSourceID implements OrderRepo.
func (repo *orderRepo) ListBySource(
	ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
) ([]*model.Order, error) {
	return listByConditions[*model.Order](
		repo.withContext(ctx).Scopes(withSourceTypeScope(sourceType)),
		map[string][]any{
			"source_id = ?": {sourceID},
		},
	)
}

func (repo *orderRepo) Create(ctx context.Context, order *model.Order) error {
	tx := repo.withContext(ctx).Create(order)
	if tx.Error != nil {
		return parseDBErr(tx.Error)
	}

	return nil
}

func (repo *orderRepo) Get(ctx context.Context, id int64) (*model.Order, error) {
	return getByID[*model.Order](repo.withContext(ctx), id)
}

func (repo *orderRepo) GetRootBySource(
	ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64,
) (*model.Order, error) {
	order := &model.Order{}
	if err := repo.withContext(ctx).
		Where("source_id = ?", sourceID).
		Scopes(withSourceTypeScope(sourceType)).
		Where("order_ref_id = 0").
		Take(order).Error; err != nil {
		return nil, parseDBErr(err)
	}

	return order, nil
}

func (repo *orderRepo) BatchGetOrders(
	ctx context.Context,
	orderIDs []int64,
) ([]*model.Order, error) {
	orders, err := listByConditions[*model.Order](
		repo.withContext(ctx), map[string][]any{
			"id IN ? ": {orderIDs},
		},
	)
	if err != nil {
		return nil, err
	}

	if len(orders) != len(orderIDs) {
		return nil, status.Error(codes.NotFound, "not all orders found")
	}

	return orders, nil
}

func (repo *orderRepo) GetForUpdate(ctx context.Context, id int64) (*model.Order, error) {
	return getByID[*model.Order](
		repo.withContext(ctx).Clauses(
			clause.Locking{
				Strength: clause.LockingStrengthUpdate,
			},
		), id,
	)
}

func (repo *orderRepo) ListTailOrder(ctx context.Context, originID int64) ([]*model.Order, error) {
	if originID == 0 {
		return nil, status.Error(codes.InvalidArgument, "originID cannot be 0")
	}

	return listByConditions[*model.Order](
		repo.withContext(ctx), map[string][]any{
			"order_ref_id = ?": {originID},
		},
	)
}

func (repo *orderRepo) ListByAppointment(ctx context.Context, appointmentID int64) ([]*model.Order, error) {
	return listByConditions[*model.Order](
		repo.withContext(ctx), map[string][]any{
			"source_id = ? AND source_type IN ?": {
				appointmentID,
				[]repohelper.SourceType{
					repohelper.SourceTypeAppointment,
					repohelper.SourceTypeNoShow,
					repohelper.SourceTypeBookingRequest,
				},
			},
		},
	)
}

func (repo *orderRepo) ListByFulfillment(ctx context.Context, fulfillmentID int64) ([]*model.Order, error) {
	return listByConditions[*model.Order](
		repo.withContext(ctx), map[string][]any{
			"source_id = ? AND source_type IN ?": {
				fulfillmentID,
				[]repohelper.SourceType{
					repohelper.SourceTypeFulfillment,
				},
			},
		},
	)
}

func (repo *orderRepo) ListBySourceForUpdate(
	ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType,
) ([]*model.Order, error) {
	return listByConditions[*model.Order](
		repo.withContext(ctx).Clauses(
			clause.Locking{
				Strength: clause.LockingStrengthUpdate,
			},
		), map[string][]any{
			"source_id = ? AND source_type = ?": {
				sourceID,
				repohelper.ConvertToSourceType(sourceType),
			},
		},
	)
}

func (repo *orderRepo) ListByAppointmentForUpdate(
	ctx context.Context, appointmentID int64,
) ([]*model.Order, error) {
	return listByConditions[*model.Order](
		repo.withContext(ctx).Clauses(
			clause.Locking{
				Strength: clause.LockingStrengthUpdate,
			},
		), map[string][]any{
			"source_id = ? AND source_type IN ?": {
				appointmentID,
				[]repohelper.SourceType{
					repohelper.SourceTypeAppointment,
					repohelper.SourceTypeNoShow,
				},
			},
		},
	)
}

func (repo *orderRepo) UpdateRefund(ctx context.Context, refundOrder *model.RefundOrder) (int64, error) {
	// 关单前直接更新订单
	//
	// 特别的：
	// 退款中可能包含 Convenience Fee。这个值本身是来自于 Payment 的，因此退款的时候需要更新订单上面的 ExtraFee，
	// 同时会引发 TotalAmount, RemainAmount 的更新.
	if !refundOrder.IsCreatedAfterCompleted() {
		// 有 Convenience Fee 退款时才插入记录.
		if refundOrder.GetRefundConvenienceFee().GreaterThan(decimal.Zero) {
			if err := repo.orderLineExtraFee.RefundConvenienceFee(ctx, refundOrder); err != nil {
				return 0, err
			}
		}

		tx := repo.withContext(ctx).Where("id = ?", refundOrder.OrderID).
			Where("paid_amount >= ?", refundOrder.RefundTotalAmount).
			Where("extra_fee_amount >= ?", refundOrder.RefundConvenienceFee).
			// 关单前是直接编辑的订单本身, totalAmount 已经在编辑时处理过了.
			// 因此这里只需要处理 ConvenienceFee 的退款造成的 totalAmount 变更.
			Where("total_amount >= ?", refundOrder.RefundConvenienceFee).
			// Remain Amount 本身在 Overpaid 的场景下就是 0，所以这里忽略对 RemainAmount 的条件要求.
			Updates(
				map[string]any{
					"paid_amount": gorm.Expr("paid_amount - ?", refundOrder.RefundTotalAmount),
					// 减去 Convenience Fee
					"extra_fee_amount": gorm.Expr("extra_fee_amount - ?", refundOrder.RefundConvenienceFee),
					// 从 Total 中减去 ConvenienceFee.
					"total_amount": gorm.Expr("total_amount - ?", refundOrder.RefundConvenienceFee),
					// Remain Amount 通过 MAX(Total - Paid, 0) 来计算
					// 这里 Total 和 Paid 都有更新, 因此需要展开一下:
					// (totalAmount - refundConvenienceFee) - (paidAmount - refundTotalAmount)
					"remain_amount": gorm.Expr(
						"GREATEST((total_amount - ?) - (paid_amount - ?), 0)",
						refundOrder.RefundConvenienceFee,
						refundOrder.RefundTotalAmount,
					),
					"payment_status": gorm.Expr(
						`CASE
	WHEN (paid_amount - ?) = 0 THEN ?
	WHEN ((paid_amount - ?) >= (total_amount - ?)) THEN ?
	ELSE ?
END`,
						refundOrder.RefundTotalAmount, orderpb.OrderModel_UNPAID,
						refundOrder.RefundTotalAmount, refundOrder.RefundConvenienceFee, orderpb.OrderModel_PAID,
						orderpb.OrderModel_PARTIAL_PAID,
					),
					"update_time": toTimestamp(time.Now()),
				},
			)

		if tx.Error != nil {
			return 0, parseDBErr(tx.Error)
		}

		if tx.RowsAffected == 0 {
			return 0, status.Error(codes.FailedPrecondition, "cannot update order for refund")
		}

		return tx.RowsAffected, nil
	}

	// 关单之后更新 RefundedAmount.
	tx := repo.withContext(ctx).Where("id = ?", refundOrder.OrderID).
		Where("paid_amount >= ?", gorm.Expr("refunded_amount + ?", refundOrder.RefundTotalAmount)).
		Updates(
			map[string]any{
				"refunded_amount": gorm.Expr("refunded_amount + ?", refundOrder.RefundTotalAmount),
				"update_time":     toTimestamp(time.Now()),
			},
		)
	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot update order")
	}

	return tx.RowsAffected, nil
}

func (repo *orderRepo) CancelOrder(ctx context.Context, order *model.Order) (int64, error) {
	order.Status = orderpb.OrderStatus_REMOVED

	tx := repo.withContext(ctx).
		Select("status", "update_time").
		Where("id = ?", order.ID).
		Updates(order)

	if tx.Error != nil {
		return 0, parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return 0, status.Error(codes.FailedPrecondition, "cannot cancel order")
	}

	return tx.RowsAffected, nil
}

func (repo *orderRepo) UpdateSourceForType(
	ctx context.Context, orderType orderpb.OrderModel_OrderType,
	oldSourceType orderpb.OrderSourceType, oldSourceID int64, newSourceType orderpb.OrderSourceType, newSourceID int64,
) error {
	tx := repo.withContext(ctx).
		Where(
			"source_id = ? AND source_type = ? AND order_type = ?",
			oldSourceID, repohelper.ConvertToSourceType(oldSourceType), orderType,
		).
		Updates(
			map[string]interface{}{
				"source_id":   newSourceID,
				"source_type": strings.ToLower(newSourceType.String()),
			},
		)

	if tx.Error != nil {
		return parseDBErr(tx.Error)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update order source")
	}

	return nil
}

func (repo *orderRepo) withContext(ctx context.Context) *gorm.DB {
	const tableName = "public.order"

	return repo.db.WithContext(ctx).Table(tableName)
}
