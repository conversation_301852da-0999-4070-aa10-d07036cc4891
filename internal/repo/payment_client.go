package repo

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	mhttp "github.com/MoeGolibrary/go-lib/http"
	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/httphelper"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type PaymentClient interface {
	GetRefundPayment(ctx context.Context, rop *model.RefundOrderPayment) (*RefundPaymentBrief, error)

	CreateRefundPayment(
		ctx context.Context, paymentID int64, reason string, rop *model.RefundOrderPayment,
	) (*RefundPaymentBrief, error)

	// GetRefundableChannel 获取订单的退款渠道.
	// 用于支持 OrderVersion < 2 的老订单的退款.
	GetRefundableChannel(
		ctx context.Context, businessID, orderID int64, amount decimal.Decimal,
	) ([]*RefundableChannel, error)
	// CreateRefundByOrder 针对订单批量发起退款.
	// 用于支持 OrderVersion < 2 的老订单的退款.
	CreateRefundByOrder(
		ctx context.Context, orderID int64, reason string, refundAmount decimal.Decimal,
		refundableChannels []*RefundableChannel,
	) error

	GetPaymentSetting(ctx context.Context, businessID int64) (*PaymentSetting, error)
	GetConvenienceFee(
		ctx context.Context, businessID int64, amount decimal.Decimal, pm paymentpb.StripePaymentMethod,
	) (decimal.Decimal, error)

	CaptureByInvoiceID(ctx context.Context, businessID, invoiceID int64) error
}

type paymentClient struct {
	cli mhttp.Client
}

func NewPaymentClient() PaymentClient {
	return &paymentClient{mhttp.NewClient("http://moego-service-payment:9204")}
}

var _ model.RefundPaymentor = (*RefundPaymentBrief)(nil)

type RefundPaymentBrief struct {
	ID                   int64                   `json:"id"`
	BusinessID           int64                   `json:"businessId"`
	Status               repohelper.RefundStatus `json:"status"`
	RefundOrderPaymentID int64                   `json:"refundOrderPaymentId"`
	StripeRefundID       string                  `json:"stripeRefundId"`
	Error                string                  `json:"error"`
}

func (rpb RefundPaymentBrief) GetID() int64 { return rpb.ID }

func (rpb RefundPaymentBrief) GetRefundStatus() orderpb.RefundOrderPaymentStatus {
	return rpb.Status.ToOrderPaymentStatus()
}

func (rpb RefundPaymentBrief) GetError() string { return rpb.Error }

func (rpb RefundPaymentBrief) GetRefundOrderPaymentID() int64 { return rpb.RefundOrderPaymentID }

func (p *paymentClient) CreateRefundPayment(
	ctx context.Context,
	paymentID int64,
	// 之前 RefundPayment 实际承担了 RefundOrder 的职责，里面包含了 Refund Reason.
	// 这里为了让老的数据模型字段保持一致，先把 Reason 带过来，后续移除.
	reason string,
	rop *model.RefundOrderPayment,
) (*RefundPaymentBrief, error) {
	const path = "/service/payment/refund"

	values := url.Values{}
	values.Set("businessId", strconv.FormatInt(rop.BusinessID, 10))

	payload, err := json.Marshal(
		map[string]any{
			"amount":               rop.GetRefundAmount().String(),
			"reason":               reason,
			"paymentId":            paymentID,
			"refundOrderPaymentId": rop.ID,
		},
	)
	if err != nil {
		return nil, err
	}

	type createHelper struct {
		Refunds []*RefundPaymentBrief `json:"refunds"`
	}

	resp, err := p.cli.Post(ctx, path, values, bytes.NewReader(payload))
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	helper, err := httphelper.ParseResponse[createHelper](resp)
	if err != nil {
		return nil, err
	}

	if len(helper.Refunds) == 0 {
		return nil, status.Error(codes.Internal, "cannot create refund payment")
	}

	for _, refund := range helper.Refunds {
		if refund.RefundOrderPaymentID == rop.ID {
			return refund, nil
		}
	}

	return nil, status.Error(codes.Internal, "cannot find created refund payment")
}

func (p *paymentClient) GetRefundPayment(ctx context.Context, rop *model.RefundOrderPayment) (
	*RefundPaymentBrief, error,
) {
	const path = "/service/payment/refund/getRefundByRefundOrderPaymentId"

	values := url.Values{}
	values.Set("businessId", strconv.FormatInt(rop.BusinessID, 10))
	values.Set("refundOrderPaymentId", strconv.FormatInt(rop.ID, 10))

	resp, err := p.cli.Get(ctx, path, values)
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	return httphelper.ParseResponse[RefundPaymentBrief](resp)
}

type RefundableChannel struct {
	PaymentMethod   string          `json:"paymentMethod"`
	PaymentID       int64           `json:"paymentId"`
	CanRefundAmount decimal.Decimal `json:"canRefundAmount"`
}

func (p *paymentClient) GetRefundableChannel(
	ctx context.Context, businessID, orderID int64, amount decimal.Decimal,
) ([]*RefundableChannel, error) {
	const path = "/service/payment/refund/check"

	payload, err := json.Marshal(
		map[string]any{
			"businessId":    businessID,
			"invoiceId":     orderID,
			"changedAmount": amount.String(),
		},
	)
	if err != nil {
		return nil, err
	}

	resp, err := p.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	type getRefundableChannelHelper struct {
		RefundAmount  decimal.Decimal      `json:"refundAmount"`
		IsCombination bool                 `json:"isCombination"`
		OrderID       int64                `json:"invoiceId"`
		Channels      []*RefundableChannel `json:"channelList"`
	}

	res, err := httphelper.ParseResponse[getRefundableChannelHelper](resp)
	if err != nil {
		return nil, err
	}

	return res.Channels, nil
}

func (p *paymentClient) CreateRefundByOrder(
	ctx context.Context,
	orderID int64,
	reason string,
	refundAmount decimal.Decimal,
	refundableChannels []*RefundableChannel,
) error {
	const path = "/service/payment/invoice/refund/submit"

	payload, err := json.Marshal(
		map[string]any{
			"invoiceId":    orderID,
			"refunds":      refundableChannels,
			"refundReason": reason,
			"refundAmount": refundAmount.String(),
		},
	)
	if err != nil {
		return err
	}

	// 这个接口正常的时候也没有返回任何 Body.
	resp, err := p.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		return merror.NewBizError(errorspb.Code(resp.StatusCode), string(msg)) //nolint:gosec // StatusCode is int32
	}

	return nil
}

type ProcessingFeePayBy int

const ProcessingFeePayByBusiness = 0

const ProcessingFeePayByClient = 1

type PaymentSetting struct {
	ProcessingFeePayBy ProcessingFeePayBy `json:"processingFeePayBy"`
}

func (p *paymentClient) GetPaymentSetting(ctx context.Context, businessID int64) (*PaymentSetting, error) {
	const path = "/service/payment/setting/info"

	params := url.Values{}
	params.Set("businessId", strconv.FormatInt(businessID, 10))

	resp, err := p.cli.Get(ctx, path, params)
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	res, err := httphelper.ParseResponse[PaymentSetting](resp)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (p *paymentClient) GetConvenienceFee(
	ctx context.Context, businessID int64, amount decimal.Decimal, pm paymentpb.StripePaymentMethod,
) (decimal.Decimal, error) {
	const path = "/service/payment/getConvenienceFee"

	params := url.Values{}
	params.Set("businessId", strconv.FormatInt(businessID, 10))
	params.Set("amount", amount.String())
	params.Set("stripePaymentMethod", strconv.Itoa(int(pm)))

	resp, err := p.cli.Get(ctx, path, params)
	if err != nil {
		return decimal.Zero, err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		return decimal.Zero, fmt.Errorf("HTTP status code error, code: %d", resp.StatusCode)
	}

	payload, err := io.ReadAll(resp.Body)
	if err != nil {
		return decimal.Zero, fmt.Errorf("read response body failed, err: %w", err)
	}

	var fee decimal.Decimal
	if err := json.Unmarshal(payload, &fee); err != nil {
		return decimal.Zero, fmt.Errorf("unmarshal fee failed, err: %w", err)
	}

	return fee, nil
}

func (p *paymentClient) CaptureByInvoiceID(ctx context.Context, businessID, invoiceID int64) error {
	const path = "/service/payment/preauth/captureByInvoiceId"

	params := url.Values{}
	params.Set("businessId", strconv.FormatInt(businessID, 10))
	params.Set("invoiceId", strconv.FormatInt(invoiceID, 10))

	resp, err := p.cli.Post(ctx, path, params, nil)
	if err != nil {
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP status code error, code: %d", resp.StatusCode)
	}

	return nil
}
