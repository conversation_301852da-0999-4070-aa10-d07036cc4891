package repo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	mhttp "github.com/MoeGolibrary/go-lib/http"
	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/repohelper"
)

type PaymentClientTestSuite struct {
	suite.Suite

	svr     *httptest.Server
	cli     PaymentClient
	handler http.HandlerFunc
}

func TestPaymentClient(t *testing.T) { suite.Run(t, new(PaymentClientTestSuite)) }

func (ts *PaymentClientTestSuite) TestCreateRefundByOrder() {
	type helper struct {
		InvoiceID    int64                `json:"invoiceId"`
		RefundReason string               `json:"refundReason"`
		Refunds      []*RefundableChannel `json:"refunds"`
		RefundAmount string               `json:"refundAmount"`
	}

	called := false
	expectedRefundAmount := decimal.NewFromInt(10)
	expected := &helper{
		InvoiceID:    110374584,
		RefundReason: "",
		Refunds: []*RefundableChannel{
			{
				PaymentMethod:   "Credit card - cof4242(visa)",
				PaymentID:       6134056,
				CanRefundAmount: decimal.NewFromFloat(15.84),
			},
		},
		RefundAmount: expectedRefundAmount.String(),
	}

	ts.handler = func(w http.ResponseWriter, r *http.Request) {
		called = true

		var req *helper

		ts.Require().NoError(json.NewDecoder(r.Body).Decode(&req))
		ts.Equal(expected, req)

		w.WriteHeader(http.StatusOK)
	}

	err := ts.cli.CreateRefundByOrder(
		context.Background(),
		expected.InvoiceID,
		expected.RefundReason,
		expectedRefundAmount,
		expected.Refunds,
	)
	ts.NoError(err)
	ts.True(called)
}

func (ts *PaymentClientTestSuite) TestGetRefundableChannel() {
	called := false
	expected := []*RefundableChannel{
		{
			PaymentMethod:   "Credit card - cof4242(visa)",
			PaymentID:       6134056,
			CanRefundAmount: decimal.NewFromFloat(15.84),
		},
	}

	ts.handler = func(w http.ResponseWriter, r *http.Request) {
		called = true

		ts.Equal(http.MethodPost, r.Method)
		ts.NoError(r.ParseForm())

		_, err := w.Write(
			[]byte(`{"channelList":[{"paymentMethod":"Credit card - cof4242(visa)","paymentId":6134056,"canRefundAmount":15.84}],"invoiceId":110374584,"isCombination":false,"refundAmount":10.00}`),
		)

		ts.Require().NoError(err)
	}

	resp, err := ts.cli.GetRefundableChannel(
		context.Background(), 110447, 110374584, decimal.NewFromFloat(10.00),
	)
	ts.NoError(err)
	ts.Equal(expected, resp)
	ts.True(called)
}

func (ts *PaymentClientTestSuite) TestCreateRefund() {
	called := false
	expected := &RefundPaymentBrief{
		ID:                   32639,
		BusinessID:           110447,
		Status:               repohelper.RefundStatusCreated,
		StripeRefundID:       "fakeStripeRefundID",
		Error:                "",
		RefundOrderPaymentID: 66666666,
	}

	ts.handler = func(w http.ResponseWriter, r *http.Request) {
		called = true

		ts.Equal(http.MethodPost, r.Method)
		ts.NoError(r.ParseForm())

		ts.Equal(fmt.Sprintf("businessId=%d", expected.BusinessID), r.Form.Encode())

		_, err := w.Write(
			[]byte(`{
    "id": 23239134,
    "businessId": 110447,
    "module": "grooming",
    "invoiceId": *********,
    "customerId": 17648289,
    "staffId": 138265,
    "method": "Credit card",
    "amount": 57.00,
    "processingFee": 1.44,
    "status": 3,
    "cardFunding": "",
    "checkNumber": null,
    "cardType": "VISA",
    "cardNumber": "1434",
    "expMonth": "6",
    "expYear": "2027",
    "signature": "",
    "paidBy": "Zihang Zhou",
    "description": "This is a deposit",
    "methodId": 8,
    "squarePaymentMethod": 2,
    "stripePaymentMethod": null,
    "isOnline": false,
    "createTime": 1731980283,
    "updateTime": 1731980295,
    "cancelReason": "",
    "locationId": "LD2JMJYGR5WN1",
    "customerName": null,
    "refunds": [
        {
            "id": 32639,
            "refundId": null,
            "method": null,
            "module": "grooming",
            "status": 0,
            "amount": 57,
            "originPaymentId": 23239134,
            "stripeRefundId": "fakeStripeRefundID",
            "reason": "Thought we were in irvine location as well and will not be in SD that day",
            "error": null,
            "createTime": 1731980538,
            "updateTime": 1731980539,
            "businessId": 110447,
            "invoiceId": *********,
            "customerId": 17648289,
            "staffId": null,
            "methodId": null,
            "refundOrderPaymentId": 66666666
        }
    ],
    "refundedAmount": 57,
    "totalCollected": 0.00,
    "companyId": 110152,
    "vendor": "Square",
    "groomingId": 58571119,
    "isPrepay": null,
    "paymentMethod": null
}`),
		)

		ts.Require().NoError(err)
	}

	resp, err := ts.cli.CreateRefundPayment(
		context.Background(), 23239134, "test reason", &model.RefundOrderPayment{
			ID:           expected.RefundOrderPaymentID,
			BusinessID:   expected.BusinessID,
			RefundAmount: decimal.NewFromInt(10),
		},
	)
	ts.NoError(err)
	ts.Equal(expected, resp)
	ts.True(called)
}

func (ts *PaymentClientTestSuite) TestGetRefundPayment_NotExists() {
	expected := merror.NewBizError(errorspb.Code_CODE_REFUND_NOT_FOUND, "")

	called := false
	ts.handler = func(w http.ResponseWriter, _ *http.Request) {
		called = true

		w.Header().Set("X-MOE-STATUS", strconv.FormatInt(int64(errorspb.Code_CODE_REFUND_NOT_FOUND), 10))
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"code":70043,"message":null,"data":null,"causedBy":null,"success":false}`))
	}

	rp, err := ts.cli.GetRefundPayment(
		context.Background(), &model.RefundOrderPayment{
			ID:         1111,
			BusinessID: 2222,
		},
	)

	ts.True(called)
	ts.Nil(rp)
	ts.Error(err)
	ts.Equal(expected, err)

	var bizErr *merror.BizError

	ok := errors.As(err, &bizErr)

	ts.True(ok)
	ts.Equal(expected, bizErr)
}

func (ts *PaymentClientTestSuite) TestCreateRefundFailed() {
	expected := merror.NewBizError(2333, "error message")

	called := false
	ts.handler = func(w http.ResponseWriter, _ *http.Request) {
		called = true

		ts.Require().NoError(
			json.NewEncoder(w).Encode(
				map[string]any{
					"code":    expected.ErrCode(),
					"message": expected.ErrMsg(),
				},
			),
		)
	}

	resp, err := ts.cli.CreateRefundPayment(
		context.Background(), 1111, "test reason", &model.RefundOrderPayment{
			ID:           2222,
			BusinessID:   3333,
			RefundAmount: decimal.NewFromInt(10),
		},
	)
	ts.Error(err)
	ts.Equal(expected, err.(*merror.BizError))
	ts.Nil(resp)
	ts.True(called)
}

func (ts *PaymentClientTestSuite) SetupSuite() {
	ts.svr = httptest.NewServer(
		http.HandlerFunc(
			func(w http.ResponseWriter, r *http.Request) {
				if ts.handler == nil {
					w.WriteHeader(http.StatusNotImplemented)
					return
				}

				ts.handler.ServeHTTP(w, r)
			},
		),
	)

	ts.cli = NewPaymentClient()
	ts.cli.(*paymentClient).cli = mhttp.NewClient(ts.svr.URL)
}

func (ts *PaymentClientTestSuite) TearDownSuite() {
	ts.svr.Close()
}

func (ts *PaymentClientTestSuite) SetupTest() {
	ts.handler = nil
}
