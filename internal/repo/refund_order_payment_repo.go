package repo

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundOrderPaymentRepo interface {
	Get(ctx context.Context, id int64) (*model.RefundOrderPayment, error)

	ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error)
	ListByRefundOrderID(ctx context.Context, refundOrderID int64) ([]*model.RefundOrderPayment, error)
	ListByStatusAndMaxUpdateTime(
		ctx context.Context, refundStatus orderpb.RefundOrderPaymentStatus, updateTime int64,
	) ([]*model.RefundOrderPayment, error)

	BatchCreate(ctx context.Context, refundOrderPayments []*model.RefundOrderPayment) error
	GetForUpdate(ctx context.Context, id int64) (*model.RefundOrderPayment, error)

	UpdateTrxCreated(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error
	UpdateRefunded(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error
	UpdateFailed(ctx context.Context, refundOrderPayment *model.RefundOrderPayment) error
}

func NewRefundOrderPaymentRepo(db *gorm.DB) RefundOrderPaymentRepo {
	return &refundOrderPaymentRepo{db: db}
}

type refundOrderPaymentRepo struct {
	db *gorm.DB
}

func (repo *refundOrderPaymentRepo) Get(ctx context.Context, refundOrderPaymentID int64) (
	*model.RefundOrderPayment, error,
) {
	return getByID[*model.RefundOrderPayment](repo.withContext(ctx), refundOrderPaymentID)
}

func (repo *refundOrderPaymentRepo) ListByRefundOrderID(ctx context.Context, refundOrderID int64) (
	[]*model.RefundOrderPayment, error,
) {
	return listByKeyID[*model.RefundOrderPayment](
		repo.withContext(ctx), "refund_order_id", refundOrderID,
	)
}

func (repo *refundOrderPaymentRepo) ListByOrderID(ctx context.Context, orderID int64) (
	[]*model.RefundOrderPayment, error,
) {
	return listByKeyID[*model.RefundOrderPayment](
		repo.withContext(ctx), "order_id", orderID,
	)
}

func (repo *refundOrderPaymentRepo) BatchCreate(
	ctx context.Context, refundOrderPayments []*model.RefundOrderPayment,
) error {
	if len(refundOrderPayments) == 0 {
		return nil
	}

	if err := repo.withContext(ctx).Create(refundOrderPayments).Error; err != nil {
		return parseDBErr(err)
	}

	return nil
}

func (repo *refundOrderPaymentRepo) GetForUpdate(ctx context.Context, id int64) (*model.RefundOrderPayment, error) {
	return getByID[*model.RefundOrderPayment](
		repo.withContext(ctx).Clauses(
			clause.Locking{
				Strength: clause.LockingStrengthUpdate,
			},
		), id,
	)
}

func (repo *refundOrderPaymentRepo) UpdateTrxCreated(
	ctx context.Context, rop *model.RefundOrderPayment,
) error {
	// 只允许从 CREATED 流向 TRANSACTION_CREATED.
	// 并且只更新状态、RefundPaymentID 和 UpdateTime.
	tx := repo.withContext(ctx).
		Where(
			&model.RefundOrderPayment{
				ID:           rop.ID,
				RefundStatus: orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED,
			},
		).
		Updates(
			map[string]any{
				"refund_status":     orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
				"refund_payment_id": rop.RefundPaymentID,
				"update_time":       toTimestamp(time.Now()),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order payment to TRX_CREATED")
	}

	return nil
}

func (repo *refundOrderPaymentRepo) UpdateRefunded(
	ctx context.Context, rop *model.RefundOrderPayment,
) error {
	// 只允许从 CREATED / TRANSACTION_CREATED 流向 REFUNDED.
	// 并且只更新 RefundPaymentID、状态、RefundTime 和 UpdateTime.
	tx := repo.withContext(ctx).
		Where(
			"id = ? AND (refund_status = ? OR refund_status = ?)",
			rop.ID,
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED.String(),
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
		).
		Updates(
			map[string]any{
				"refund_payment_id": rop.RefundPaymentID,
				"refund_status":     orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_REFUNDED.String(),
				"refund_time":       toTimestamp(time.Unix(rop.RefundTime, 0)),
				"update_time":       toTimestamp(time.Now()),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order payment to REFUNDED")
	}

	return nil
}

func (repo *refundOrderPaymentRepo) UpdateFailed(
	ctx context.Context, rop *model.RefundOrderPayment,
) error {
	// 只允许从 CREATED / TRANSACTION_CREATED 流向 FAILED.
	// 并且只更新状态、RefundStatusReason、FailTime 和 UpdateTime.
	tx := repo.withContext(ctx).
		Where(
			"id = ? AND (refund_status = ? OR refund_status = ?)",
			rop.ID,
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED.String(),
			orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED.String(),
		).
		Updates(
			map[string]any{
				"refund_status":        orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_FAILED.String(),
				"refund_status_reason": rop.RefundStatusReason,
				"fail_time":            toTimestamp(time.Unix(rop.FailTime, 0)),
				"update_time":          toTimestamp(time.Unix(rop.UpdateTime, 0)),
			},
		)

	if err := tx.Error; err != nil {
		return parseDBErr(err)
	}

	if tx.RowsAffected == 0 {
		return status.Error(codes.FailedPrecondition, "cannot update refund order payment to REFUNDED")
	}

	return nil
}

func (repo *refundOrderPaymentRepo) ListByStatusAndMaxUpdateTime(
	ctx context.Context, refundStatus orderpb.RefundOrderPaymentStatus, updateTime int64,
) ([]*model.RefundOrderPayment, error) {
	return listByConditions[*model.RefundOrderPayment](
		repo.withContext(ctx),
		map[string][]any{
			"refund_status = ? AND update_time <= to_timestamp(?)": {refundStatus.String(), updateTime},
		},
	)
}

func (repo *refundOrderPaymentRepo) withContext(ctx context.Context) *gorm.DB {
	return repo.db.WithContext(ctx).Table("public.refund_order_payment")
}
