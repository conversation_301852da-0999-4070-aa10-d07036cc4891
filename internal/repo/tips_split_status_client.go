package repo

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	redislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/redis"
	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

const (
	// tips_split:${source_type}:${source_id}:updated
	tipsSplitKeyFormat = "tips_split:%s:%s:updated"
	// Set the expiration to 1 month (30 days)
	expiration = 30 * 24 * time.Hour
)

func getTipsSplitKey(sourceType orderpb.OrderSourceType, sourceID int64) string {
	return fmt.Sprintf(tipsSplitKeyFormat, sourceType.String(), strconv.FormatInt(sourceID, 10))
}

type TipsSplitStatusClient interface {
	// get current status
	Get(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) (bool, error)
	// clear current status
	Del(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error
	// set current status
	Set(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error
}

type tipsSplitStatusClient struct {
	cli redis.UniversalClient
}

func NewTipsSplitStatusClient(cli redis.UniversalClient) TipsSplitStatusClient {
	return &tipsSplitStatusClient{cli: cli}
}

func (ts *tipsSplitStatusClient) Get(
	ctx context.Context,
	sourceType orderpb.OrderSourceType,
	sourceID int64,
) (bool, error) {
	statusCmd := ts.cli.Get(ctx, getTipsSplitKey(sourceType, sourceID))
	zlog.Debug(ctx, "get tips split status",
		zap.Any("value", statusCmd))

	switch err := statusCmd.Err(); {
	case err == nil:
		return true, nil

	case errors.Is(err, redislib.Nil):
		return false, nil

	default:
		return false, err
	}
}

func (ts *tipsSplitStatusClient) Del(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	intCmd := ts.cli.Del(ctx, getTipsSplitKey(sourceType, sourceID))
	zlog.Info(ctx, "clear tips split status",
		zap.Any("value", intCmd))
	// Return any error encountered
	return intCmd.Err()
}

func (ts *tipsSplitStatusClient) Set(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) error {
	statusCmd := ts.cli.Set(ctx,
		getTipsSplitKey(sourceType, sourceID),
		time.Now().UnixMilli(),
		expiration)
	zlog.Info(ctx, "set tips split status",
		zap.String("value", statusCmd.Val()))
	// Return any error encountered
	return statusCmd.Err()
}
