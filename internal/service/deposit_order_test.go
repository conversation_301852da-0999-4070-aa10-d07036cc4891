package service

import (
	"cmp"
	"context"
	"slices"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	mocks "github.com/MoeGolibrary/moego-svc-order-v2/internal/mocks/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
)

func TestOrderService_GetDepositDetail_Empty(t *testing.T) {
	ctx := context.Background()

	depositChangeLogRepo := new(mocks.DepositChangeLogRepo)

	depositOrderService := NewDepositOrderService(depositChangeLogRepo, nil, nil)

	// Set up the mocks to return empty results
	depositOrderID := int64(1)
	depositChangeLogRepo.EXPECT().ListByDepositOrderID(ctx, depositOrderID).Return(nil, nil)

	result, err := depositOrderService.GetDepositSummary(ctx, &model.Order{
		ID:             depositOrderID,
		OrderVersion:   model.OrderVersionRefund,
		OrderType:      orderpb.OrderModel_DEPOSIT,
		CurrencyCode:   "USD",
		SubTotalAmount: decimal.NewFromInt(10),
		TotalAmount:    decimal.NewFromFloat(10.66),
		PaidAmount:     decimal.Zero,
		RemainAmount:   decimal.NewFromFloat(10.66),
		RefundedAmount: decimal.Zero,
	})

	assert.NoError(t, err)
	assert.True(t, proto.Equal(&ordersvcpb.GetDepositDetailResponse{
		CollectedAmount: money.FromDecimal(decimal.Zero, "USD"),
		ReversedAmount:  money.FromDecimal(decimal.Zero, "USD"),
		DeductedAmount:  money.FromDecimal(decimal.Zero, "USD"),
		Balance:         money.FromDecimal(decimal.Zero, "USD"),
	}, result))

	// Assert that the mocks were called with the expected arguments
	depositChangeLogRepo.AssertExpectations(t)
}

func TestOrderService_GetDepositDetail_TopUp(t *testing.T) {
	ctx := context.Background()

	depositChangeLogRepo := new(mocks.DepositChangeLogRepo)

	depositOrderService := NewDepositOrderService(depositChangeLogRepo, nil, nil)

	// Set up the mocks to return empty results
	depositOrderID := int64(1)
	depositChangeLogRepo.EXPECT().ListByDepositOrderID(ctx, depositOrderID).Return([]*model.DepositChangeLog{
		{
			ID:             1,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(10),
			Balance:        decimal.NewFromInt(10),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
		},
	}, nil)

	result, err := depositOrderService.GetDepositSummary(ctx, &model.Order{
		ID:             depositOrderID,
		OrderVersion:   model.OrderVersionRefund,
		OrderType:      orderpb.OrderModel_DEPOSIT,
		CurrencyCode:   "USD",
		SubTotalAmount: decimal.NewFromInt(10),
		TotalAmount:    decimal.NewFromFloat(10.66),
		PaidAmount:     decimal.NewFromFloat(10.66),
		RemainAmount:   decimal.Zero,
		RefundedAmount: decimal.Zero,
	})

	assert.NoError(t, err)
	assert.True(t, proto.Equal(&ordersvcpb.GetDepositDetailResponse{
		CollectedAmount: money.FromDecimal(decimal.NewFromInt(10), "USD"),
		ReversedAmount:  money.FromDecimal(decimal.Zero, "USD"),
		DeductedAmount:  money.FromDecimal(decimal.Zero, "USD"),
		Balance:         money.FromDecimal(decimal.NewFromInt(10), "USD"),
	}, result))

	depositChangeLogRepo.AssertExpectations(t)
}

func TestOrderService_GetDepositDetail_TopUpRefund(t *testing.T) {
	ctx := context.Background()

	depositChangeLogRepo := new(mocks.DepositChangeLogRepo)

	depositOrderService := NewDepositOrderService(depositChangeLogRepo, nil, nil)

	// Set up the mocks to return empty results
	depositOrderID := int64(1)
	depositChangeLogRepo.EXPECT().ListByDepositOrderID(ctx, depositOrderID).Return([]*model.DepositChangeLog{
		{
			ID:             1,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(10),
			Balance:        decimal.NewFromInt(10),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
		},
		{
			ID:             2,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_OVERPAYMENT_REVERSAL,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(2),
			Balance:        decimal.NewFromInt(8),
			CurrencyCode:   "USD",
			PreviousLogID:  1,
		},
	}, nil)

	result, err := depositOrderService.GetDepositSummary(ctx, &model.Order{
		ID:             depositOrderID,
		OrderVersion:   model.OrderVersionRefund,
		OrderType:      orderpb.OrderModel_DEPOSIT,
		CurrencyCode:   "USD",
		SubTotalAmount: decimal.NewFromInt(10),
		TotalAmount:    decimal.NewFromFloat(10.66),
		PaidAmount:     decimal.NewFromFloat(10.66),
		RemainAmount:   decimal.Zero,
		RefundedAmount: decimal.Zero,
	})

	assert.NoError(t, err)
	assert.True(t, proto.Equal(&ordersvcpb.GetDepositDetailResponse{
		CollectedAmount: money.FromDecimal(decimal.NewFromInt(10), "USD"),
		ReversedAmount:  money.FromDecimal(decimal.NewFromInt(2), "USD"),
		DeductedAmount:  money.FromDecimal(decimal.Zero, "USD"),
		Balance:         money.FromDecimal(decimal.NewFromInt(8), "USD"),
	}, result))

	depositChangeLogRepo.AssertExpectations(t)
}

func TestOrderService_GetDepositDetail_Deducted(t *testing.T) {
	ctx := context.Background()

	depositChangeLogRepo := new(mocks.DepositChangeLogRepo)

	depositOrderService := NewDepositOrderService(depositChangeLogRepo, nil, nil)

	// Set up the mocks to return empty results
	depositOrderID := int64(1)
	depositChangeLogRepo.EXPECT().ListByDepositOrderID(ctx, depositOrderID).Return([]*model.DepositChangeLog{
		{
			ID:             1,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(10),
			Balance:        decimal.NewFromInt(10),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
		},
		{
			ID:             1,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_DEDUCTION,
			DestOrderID:    1000,
			ChangedAmount:  decimal.NewFromInt(10),
			Balance:        decimal.Zero,
			CurrencyCode:   "USD",
			PreviousLogID:  1,
		},
	}, nil)

	result, err := depositOrderService.GetDepositSummary(ctx, &model.Order{
		ID:             depositOrderID,
		OrderVersion:   model.OrderVersionRefund,
		OrderType:      orderpb.OrderModel_DEPOSIT,
		CurrencyCode:   "USD",
		SubTotalAmount: decimal.NewFromInt(10),
		TotalAmount:    decimal.NewFromFloat(10.66),
		PaidAmount:     decimal.NewFromFloat(10.66),
		RemainAmount:   decimal.Zero,
		RefundedAmount: decimal.Zero,
	})

	assert.NoError(t, err)
	assert.True(t, proto.Equal(&ordersvcpb.GetDepositDetailResponse{
		CollectedAmount: money.FromDecimal(decimal.NewFromInt(10), "USD"),
		ReversedAmount:  money.FromDecimal(decimal.Zero, "USD"),
		DeductedAmount:  money.FromDecimal(decimal.NewFromInt(10), "USD"),
		Balance:         money.FromDecimal(decimal.Zero, "USD"),
	}, result))

	depositChangeLogRepo.AssertExpectations(t)
}

func TestOrderService_GetDepositDetail_Overpaid(t *testing.T) {
	ctx := context.Background()

	depositChangeLogRepo := new(mocks.DepositChangeLogRepo)

	depositOrderService := NewDepositOrderService(depositChangeLogRepo, nil, nil)

	// -- Overpaid not refunded
	depositOrderID := int64(1)
	depositChangeLogRepo.EXPECT().ListByDepositOrderID(ctx, depositOrderID).Return([]*model.DepositChangeLog{
		{
			ID:             1,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(10),
			Balance:        decimal.NewFromInt(10),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
		},
		{
			ID:             2,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_OVERPAYMENT_REVERSAL,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(2),
			Balance:        decimal.NewFromInt(8),
			CurrencyCode:   "USD",
			PreviousLogID:  1,
		},
		{
			ID:             3,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_DEDUCTION,
			DestOrderID:    1000,
			ChangedAmount:  decimal.NewFromInt(5),
			Balance:        decimal.NewFromInt(3),
			CurrencyCode:   "USD",
			PreviousLogID:  2,
		},
	}, nil)

	result, err := depositOrderService.GetDepositSummary(ctx, &model.Order{
		ID:             depositOrderID,
		OrderVersion:   model.OrderVersionRefund,
		OrderType:      orderpb.OrderModel_DEPOSIT,
		CurrencyCode:   "USD",
		SubTotalAmount: decimal.NewFromInt(10),
		TotalAmount:    decimal.NewFromFloat(10.66),
		PaidAmount:     decimal.NewFromFloat(10.66),
		RemainAmount:   decimal.Zero,
		RefundedAmount: decimal.Zero,
	})

	assert.NoError(t, err)
	assert.True(t, proto.Equal(&ordersvcpb.GetDepositDetailResponse{
		CollectedAmount: money.FromDecimal(decimal.NewFromInt(10), "USD"),
		ReversedAmount:  money.FromDecimal(decimal.NewFromInt(2), "USD"),
		DeductedAmount:  money.FromDecimal(decimal.NewFromInt(5), "USD"),
		Balance:         money.FromDecimal(decimal.NewFromInt(3), "USD"),
	}, result))

	// -- Overpaid and refunded
	depositOrderID = int64(2)
	depositChangeLogRepo.EXPECT().ListByDepositOrderID(ctx, depositOrderID).Return([]*model.DepositChangeLog{
		{
			ID:             1,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_INCREASE,
			Reason:         orderpb.DepositChangeReason_TOP_UP,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(10),
			Balance:        decimal.NewFromInt(10),
			CurrencyCode:   "USD",
			PreviousLogID:  0,
		},
		{
			ID:             2,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_OVERPAYMENT_REVERSAL,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(2),
			Balance:        decimal.NewFromInt(8),
			CurrencyCode:   "USD",
			PreviousLogID:  1,
		},
		{
			ID:             3,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_DEDUCTION,
			DestOrderID:    1000,
			ChangedAmount:  decimal.NewFromInt(5),
			Balance:        decimal.NewFromInt(3),
			CurrencyCode:   "USD",
			PreviousLogID:  2,
		},
		{
			ID:             4,
			DepositOrderID: depositOrderID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_OVERPAYMENT_REVERSAL,
			DestOrderID:    0,
			ChangedAmount:  decimal.NewFromInt(3),
			Balance:        decimal.Zero,
			CurrencyCode:   "USD",
			PreviousLogID:  3,
		},
	}, nil)

	result, err = depositOrderService.GetDepositSummary(ctx, &model.Order{
		ID:             depositOrderID,
		OrderVersion:   model.OrderVersionRefund,
		OrderType:      orderpb.OrderModel_DEPOSIT,
		CurrencyCode:   "USD",
		SubTotalAmount: decimal.NewFromInt(10),
		TotalAmount:    decimal.NewFromFloat(10.66),
		PaidAmount:     decimal.NewFromFloat(10.66),
		RemainAmount:   decimal.Zero,
		RefundedAmount: decimal.Zero,
	})

	assert.NoError(t, err)
	assert.True(t, proto.Equal(&ordersvcpb.GetDepositDetailResponse{
		CollectedAmount: money.FromDecimal(decimal.NewFromInt(10), "USD"),
		ReversedAmount:  money.FromDecimal(decimal.NewFromInt(5), "USD"),
		DeductedAmount:  money.FromDecimal(decimal.NewFromInt(5), "USD"),
		Balance:         money.FromDecimal(decimal.Zero, "USD"),
	}, result))

	depositChangeLogRepo.AssertExpectations(t)
}

func TestDepositOrderService_CreateDepositOrder_NoPriceItems(t *testing.T) {
	orderRepo := mocks.NewOrderRepo(t)
	orderItemRepo := mocks.NewOrderItemRepo(t)
	tx := mocks.NewOrderTX(t)
	txRepo := mocks.NewTXRepo(t)
	srv := NewDepositOrderService(nil, nil, txRepo)

	txRepo.EXPECT().Tx(mock.Anything).RunAndReturn(func(fn func(repo.OrderTX) error) error { return fn(tx) })
	tx.EXPECT().Order().Return(orderRepo)
	tx.EXPECT().OrderItem().Return(orderItemRepo)

	orderRepo.EXPECT().ListBySourceForUpdate(mock.Anything, int64(1001), orderpb.OrderSourceType_APPOINTMENT).Return(nil, nil)
	orderRepo.EXPECT().
		Create(mock.Anything, mock.MatchedBy(func(actual *model.Order) bool {
			return actual.ID == 0 && actual.OrderVersion == model.OrderVersionImmutableOrder &&
				actual.CompanyID == 101 && actual.BusinessID == 201 && actual.CustomerID == 401 &&
				actual.CreateBy == 301 && actual.UpdateBy == 301 &&
				actual.SourceType == "appointment" && actual.SourceID == 1001 &&
				actual.CurrencyCode == "USD" &&
				actual.OrderRefID == 0 && actual.OrderType == orderpb.OrderModel_DEPOSIT &&
				actual.Status == orderpb.OrderStatus_CREATED && actual.PaymentStatus == orderpb.OrderModel_UNPAID
		})).
		RunAndReturn(func(_ context.Context, order *model.Order) error {
			order.ID = 2001
			return nil
		})
	orderItemRepo.EXPECT().BatchCreate(mock.Anything, mock.MatchedBy(func(actual []*model.OrderItem) bool {
		if len(actual) != 1 {
			return false
		}

		line := actual[0]
		return line.ItemType == model.OrderItemTypeDeposit && line.OrderID == 2001 &&
			line.CurrencyCode == "USD" && line.Quantity == 1 && line.UnitPrice.String() == "10" &&
			line.SubTotalAmount.String() == "10" && line.TotalAmount.String() == "10"
	})).Return(nil)

	od, err := srv.CreateDepositOrder(context.Background(), &ordersvcpb.CreateDepositOrderRequest{
		CompanyId:          101,
		BusinessId:         201,
		StaffId:            301,
		CustomerId:         401,
		SourceType:         orderpb.OrderSourceType_APPOINTMENT,
		SourceId:           1001,
		DepositAmount:      money.FromDecimal(decimal.NewFromInt(10), "USD"),
		DepositDescription: "Unit test",
	})

	assert.NoError(t, err)
	assert.Len(t, od.OrderItems, 1)
	assert.Empty(t, od.OrderItems[0].SubTotalItems)
}

func TestDepositOrderService_CreateDepositOrder_WithPriceItems(t *testing.T) {
	orderRepo := mocks.NewOrderRepo(t)
	orderItemRepo := mocks.NewOrderItemRepo(t)
	tx := mocks.NewOrderTX(t)
	txRepo := mocks.NewTXRepo(t)
	srv := NewDepositOrderService(nil, nil, txRepo)

	txRepo.EXPECT().Tx(mock.Anything).RunAndReturn(func(fn func(repo.OrderTX) error) error { return fn(tx) })
	tx.EXPECT().Order().Return(orderRepo)
	tx.EXPECT().OrderItem().Return(orderItemRepo)

	orderRepo.EXPECT().ListBySourceForUpdate(mock.Anything, int64(1001), orderpb.OrderSourceType_APPOINTMENT).Return(nil, nil)
	orderRepo.EXPECT().
		Create(mock.Anything, mock.MatchedBy(func(actual *model.Order) bool {
			return actual.ID == 0 && actual.OrderVersion == model.OrderVersionImmutableOrder &&
				actual.CompanyID == 101 && actual.BusinessID == 201 && actual.CustomerID == 401 &&
				actual.CreateBy == 301 && actual.UpdateBy == 301 &&
				actual.SourceType == "appointment" && actual.SourceID == 1001 &&
				actual.CurrencyCode == "USD" &&
				actual.OrderRefID == 0 && actual.OrderType == orderpb.OrderModel_DEPOSIT &&
				actual.Status == orderpb.OrderStatus_CREATED && actual.PaymentStatus == orderpb.OrderModel_UNPAID
		})).
		RunAndReturn(func(_ context.Context, order *model.Order) error {
			order.ID = 2001
			return nil
		})
	orderItemRepo.EXPECT().BatchCreate(mock.Anything, mock.MatchedBy(func(actual []*model.OrderItem) bool {
		if len(actual) != 1 {
			return false
		}

		line := actual[0]
		return line.ItemType == model.OrderItemTypeDeposit && line.OrderID == 2001 &&
			line.CurrencyCode == "USD" && line.Quantity == 1 && line.UnitPrice.String() == "85" &&
			line.SubTotalAmount.String() == "85" && line.TotalAmount.String() == "85"
	})).Return(nil)

	od, err := srv.CreateDepositOrder(context.Background(), &ordersvcpb.CreateDepositOrderRequest{
		CompanyId:          101,
		BusinessId:         201,
		StaffId:            301,
		CustomerId:         401,
		SourceType:         orderpb.OrderSourceType_APPOINTMENT,
		SourceId:           1001,
		DepositAmount:      money.FromDecimal(decimal.NewFromInt(85), "USD"),
		DepositDescription: "Unit test",
		DepositPriceDetail: &orderpb.PriceDetailModel{
			PriceItems: []*orderpb.PriceDetailModel_PriceItem{
				{
					Name:       "Bath - small",
					Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
					Quantity:   2,
					UnitPrice:  money.FromDecimal(decimal.NewFromInt(10), "USD"),
					ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectId:   5001,
				},
				{
					Name:       "Bath - small",
					Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
					Quantity:   1,
					UnitPrice:  money.FromDecimal(decimal.NewFromInt(15), "USD"),
					ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectId:   5001,
				},
				{
					Name:       "Bath - large",
					Operator:   orderpb.PriceDetailModel_PriceItem_ADD,
					Quantity:   1,
					UnitPrice:  money.FromDecimal(decimal.NewFromInt(50), "USD"),
					ObjectType: orderpb.ItemType_ITEM_TYPE_SERVICE,
					ObjectId:   5002,
				},
			},
		},
	})

	assert.NoError(t, err)
	assert.Len(t, od.OrderItems, 1)
	assert.Len(t, od.OrderItems[0].SubTotalItems, 2)

	slices.SortFunc(od.OrderItems[0].SubTotalItems, func(a, b *model.PriceItem) int {
		if cmp.Compare(a.ObjectType, b.ObjectType) != 0 {
			return cmp.Compare(a.ObjectType, b.ObjectType)
		}

		return cmp.Compare(a.ObjectID, b.ObjectID)
	})
	assert.Equal(t, int64(5001), od.OrderItems[0].SubTotalItems[0].ObjectID)
	assert.Equal(t, int32(1), od.OrderItems[0].SubTotalItems[0].Quantity)
	assert.Equal(t, orderpb.PriceDetailModel_PriceItem_ADD, od.OrderItems[0].SubTotalItems[0].Operator)
	assert.Equal(t, "35", od.OrderItems[0].SubTotalItems[0].UnitPrice.String())
	assert.Equal(t, "35", od.OrderItems[0].SubTotalItems[0].Subtotal.String())
	assert.Equal(t, int64(5002), od.OrderItems[0].SubTotalItems[1].ObjectID)
	assert.Equal(t, int32(1), od.OrderItems[0].SubTotalItems[1].Quantity)
	assert.Equal(t, orderpb.PriceDetailModel_PriceItem_ADD, od.OrderItems[0].SubTotalItems[1].Operator)
	assert.Equal(t, "50", od.OrderItems[0].SubTotalItems[1].UnitPrice.String())
	assert.Equal(t, "50", od.OrderItems[0].SubTotalItems[1].Subtotal.String())
}
