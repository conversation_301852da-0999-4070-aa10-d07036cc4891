package helper

import (
	"sort"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundHelper struct {
	od   *model.OrderDetail
	rods []*model.RefundOrderDetail
	dd   *model.DepositDetail
}

func NewRefundHelper(od *model.OrderDetail, rods []*model.RefundOrderDetail, dd *model.DepositDetail) *RefundHelper {
	return &RefundHelper{
		od:   od,
		rods: rods,
		dd:   dd,
	}
}

func (rh *RefundHelper) initRefundOrder() *model.RefundOrder {
	od := rh.od.Order

	return &model.RefundOrder{
		ID:                   0,
		OrderID:              od.ID,
		CompanyID:            od.CompanyID,
		BusinessID:           od.BusinessID,
		StaffID:              0,
		CustomerID:           od.CustomerID,
		RefundMode:           orderpb.RefundMode_REFUND_MODE_UNSPECIFIED,
		RefundReason:         "",
		OrderStatusSnapshot:  od.Status,
		CurrencyCode:         od.CurrencyCode,
		RefundTotalAmount:    decimal.Zero,
		RefundItemSubTotal:   decimal.Zero,
		RefundDiscountAmount: decimal.Zero,
		RefundTipsAmount:     decimal.Zero,
		RefundConvenienceFee: decimal.Zero,
		RefundTaxAmount:      decimal.Zero,
		RefundOrderStatus:    orderpb.RefundOrderStatus_REFUND_ORDER_STATUS_CREATED,
		CreateTime:           time.Now().Unix(),
		RefundTime:           0,
		UpdateTime:           time.Now().Unix(),
	}
}

func (rh *RefundHelper) initRefundOrderPayment(op *model.OrderPayment) *model.RefundOrderPayment {
	od := rh.od.Order

	return &model.RefundOrderPayment{
		ID:             0,
		OrderID:        od.ID,
		OrderPaymentID: op.ID,
		RefundOrderID:  0,
		CompanyID:      op.CompanyID,
		BusinessID:     od.BusinessID,
		StaffID:        0,
		CustomerID:     od.CustomerID,
		RefundPaymentMethod: model.RefundPaymentMethod{
			ID:     op.PaymentMethod.ID,
			Method: op.PaymentMethod.Method,
			Extra:  model.RefundPaymentMethodExtra{},
			Vendor: op.PaymentMethod.Vendor,
		},
		CurrencyCode:       op.CurrencyCode,
		RefundAmount:       decimal.Decimal{},
		Description:        "",
		RefundStatus:       orderpb.RefundOrderPaymentStatus_REFUND_ORDER_PAYMENT_STATUS_CREATED,
		RefundStatusReason: "",
		CreateTime:         time.Now().Unix(),
		RefundTime:         0,
		UpdateTime:         time.Now().Unix(),
	}
}

func (rh *RefundHelper) initRefundOrderItem(oi *model.OrderItem) *model.RefundOrderItem {
	od := rh.od.Order

	return &model.RefundOrderItem{
		ID:              0,
		OrderID:         oi.OrderID,
		OrderItemID:     oi.ID,
		RefundOrderID:   0,
		CompanyID:       0,
		BusinessID:      oi.BusinessID,
		StaffID:         oi.StaffID,
		StaffIDs:        oi.StaffIDs,
		CustomerID:      od.CustomerID,
		ItemType:        oi.ItemType,
		ItemID:          oi.ObjectID,
		ItemName:        oi.Name,
		ItemDescription: oi.Description,
		ItemUnitPrice:   oi.UnitPrice.Copy(),
		CurrencyCode:    oi.CurrencyCode,
		RefundItemMode:  orderpb.RefundItemMode_REFUND_ITEM_MODE_UNSPECIFIED,
		RefundTax: model.RefundTax{
			ID:     oi.Tax.ID,
			Name:   oi.Tax.Name,
			Rate:   oi.Tax.Rate,
			Amount: decimal.Zero,
		},
		RefundTotalAmount:    decimal.Zero,
		RefundQuantity:       0,
		RefundAmount:         decimal.Zero,
		RefundDiscountAmount: decimal.Zero,
		CreateTime:           time.Now().Unix(),
	}
}

// calculateByRateAndMax calculates a partial refund based on a rate and ensures it does not exceed a maximum limit.
// It is only for non-tax amount.
func (*RefundHelper) calculateByRateAndMax(amount, rate, maxAmount decimal.Decimal) decimal.Decimal {
	if maxAmount.IsZero() {
		return decimal.Zero
	}

	if partial := amount.Mul(rate).RoundBank(AmountPrecision); partial.LessThanOrEqual(maxAmount) {
		return partial
	}

	return maxAmount
}

// calculateTaxByRateAndMax calculates a partial refund based on a rate and ensures it does not exceed a maximum limit.
// It is only for non-tax amount.
func (*RefundHelper) calculateTaxByRateAndMax(amount, rate, maxAmount decimal.Decimal) decimal.Decimal {
	if maxAmount.IsZero() {
		return decimal.Zero
	}

	if partial := amount.Mul(rate).Round(AmountPrecision); partial.LessThanOrEqual(maxAmount) {
		return partial
	}

	return maxAmount
}

// calculateRate calculates the rate for non-tax amount.
func (*RefundHelper) calculateRate(a, b decimal.Decimal) decimal.Decimal {
	if b.IsZero() {
		return decimal.Zero
	}

	return a.Div(b).RoundBank(RatePrecision)
}

// calculateTaxRate calculates the rate for tax.
func (*RefundHelper) calculateTaxRate(a, b decimal.Decimal) decimal.Decimal {
	if b.IsZero() {
		return decimal.Zero
	}

	return a.Div(b).Round(RatePrecision)
}

// BuildRefundablePayments 返回可退的 Payment.
// 逻辑从 server-payment 的 RefundChanelDTO getUsefulRefundChannel 抄来的.
// 返回的第一个数组为可退的 Payment，第二个 Boolean 表示是否要多个 Payment 组合退款.
func (rh *RefundHelper) BuildRefundablePayments(
	orderPayments []*model.OrderPayment,
	refundAmount decimal.Decimal,
	refundAmountFlags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
) ([]*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment, bool, error) {
	// 过滤支付成功的并且还有可退金额的 OrderPayment.
	orderPayments = lo.Filter(
		orderPayments,
		func(it *model.OrderPayment, _ int) bool {
			return it.PaymentStatus == orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID &&
				it.GetRefundableAmount().GreaterThan(decimal.Zero)
		},
	)

	getRefundableAmount := func(op *model.OrderPayment) decimal.Decimal {
		return rh.getOrderPaymentRefundableAmount(op, refundAmountFlags)
	}

	// 按可退金额从小到大排序.
	// 相等情况下，退 orderPaymentID 更小的.
	sort.Slice(
		orderPayments, func(i, j int) bool {
			opi := orderPayments[i]
			opj := orderPayments[j]

			// 第一顺序： Credit Card
			// 第二顺序： Refundable Amount
			// 第三顺序： 发起支付的时间从早到晚（即 orderPaymentID 从小到大）
			if opi.PaymentMethod.IsCreditCard() == opj.PaymentMethod.IsCreditCard() {
				// 都是 CC 或者都 不是 CC, 比较 Refundable Amount（扣除 RefundableConvenienceFee 后）.
				rai := getRefundableAmount(opi)
				raj := getRefundableAmount(opj)

				if rai.Equal(raj) {
					// Refundable Amount 相等 比较 ID.
					return opi.ID < opj.ID
				}

				return rai.LessThan(raj)
			}

			// 一个是 Credit Card, 另外一个不是时
			return opi.PaymentMethod.IsCreditCard()
		},
	)

	// 判断一下所有 Refundable 的金额加起来是否可以覆盖 Amount
	refundableAmount := decimal.Zero
	for _, op := range orderPayments {
		refundableAmount = refundableAmount.Add(getRefundableAmount(op))
	}

	if refundableAmount.LessThan(refundAmount) {
		return nil, false, status.Error(codes.InvalidArgument, "refundable amount is not enough")
	}

	// 单个 OrderPayment 可以覆盖时，仅返回可以覆盖的 OrderPayment.
	needCombineOrderPayments := false
	candidateOrderPayments := lo.Filter(
		orderPayments,
		func(it *model.OrderPayment, _ int) bool {
			return getRefundableAmount(it).GreaterThanOrEqual(refundAmount)
		},
	)

	// 没有可以单独覆盖全部退款的 OrderPayments 时，返回所有的 Order Payments.
	if len(candidateOrderPayments) == 0 {
		needCombineOrderPayments = true
		candidateOrderPayments = orderPayments
	}

	return lo.Map(
		candidateOrderPayments,
		func(op *model.OrderPayment, _ int) *ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment {
			return op.ToRefundablePayment()
		},
	), needCombineOrderPayments, nil
}

// PreviewRefundOrderPayments 分摊退款金额 Amount 到指定的 paymentIDs 所对应的 OrderPayment 上.
func (rh *RefundHelper) PreviewRefundOrderPayments(
	refundablePayments []*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment,
	paymentIDs []int64,
	amount decimal.Decimal,
	refundAmountFlags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
) ([]*model.RefundOrderPayment, error) {
	if len(paymentIDs) == 0 || amount.IsZero() {
		// 不需要计算，快速退出
		return nil, nil
	}

	idToPayment := lo.KeyBy(rh.od.OrderPayments, func(it *model.OrderPayment) int64 { return it.ID })

	// 检查 Payment ID 的有效性.
	for _, paymentID := range paymentIDs {
		if _, found := idToPayment[paymentID]; !found {
			return nil, status.Errorf(codes.InvalidArgument, "not found payment[%d]", paymentID)
		}
	}

	idToSelectedPayment := lo.SliceToMap(
		paymentIDs,
		func(id int64) (int64, bool) { return id, true },
	)

	refundOrderPayments := make([]*model.RefundOrderPayment, 0, len(paymentIDs))
	restRefundAmount := amount

	// 以 RefundableOrderPayment 的顺序退款.
	for _, rp := range refundablePayments {
		if restRefundAmount.IsZero() {
			break
		}

		paymentID := rp.GetOrderPaymentId()
		if !idToSelectedPayment[paymentID] {
			continue
		}

		payment := idToPayment[paymentID]

		var rop *model.RefundOrderPayment

		restRefundAmount, rop = rh.splitRefundAmountToOrderPayment(
			restRefundAmount, refundAmountFlags, payment,
		)

		// 跳过分摊到退 0 元的记录.
		if rop.GetRefundAmount().IsZero() {
			continue
		}

		refundOrderPayments = append(refundOrderPayments, rop)
	}

	if restRefundAmount.GreaterThan(decimal.Zero) {
		return nil, status.Error(codes.InvalidArgument, "refundable amount is not enough")
	}

	return refundOrderPayments, nil
}

// 分摊 refundAmount 的退款金额给指定的 OrderPayment.
// refundAmountFlags 是传入的 refundAmount 的标记.
//
// 返回的第一个值表示当前 Order Payment 分摊之后，还剩多少需要分摊.
func (rh *RefundHelper) splitRefundAmountToOrderPayment(
	refundAmount decimal.Decimal,
	refundAmountFlags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
	orderPayment *model.OrderPayment,
) (decimal.Decimal, *model.RefundOrderPayment) {
	rop := rh.initRefundOrderPayment(orderPayment)

	refundableAmount := rh.getOrderPaymentRefundableAmount(orderPayment, refundAmountFlags)

	// 需要分摊的金额超过了当前 Order Payment 能分摊的金额.
	// 这里触发 Order Payment 的全退轧差逻辑.
	if refundAmount.GreaterThanOrEqual(refundableAmount) {
		// 这里直接用 GetRefundableAmount 轧差.
		rop.RefundAmount = orderPayment.GetRefundableAmount()
		rop.RefundConvenienceFee = orderPayment.GetRefundableConvenienceFee()

		return refundAmount.Sub(refundableAmount), rop
	}

	// 当前 OrderPayment 可以全额覆盖 Refund Amount.
	// 所以 refundAmount 就是当前 Order Payment 需要退的钱.

	// RefundAmount 含 Convenience Fee 的时候.
	// 计算得到的当前 Order Payment 的退钱也是包含 Convenience Fee 的钱.
	// 需要乘以 Convenience Fee 占比来得到其中包含了多少 Convenience Fee.
	if refundAmountFlags.GetIsConvenienceFeeIncluded() {
		// Payment 总金额含税，因此这里需要按税计算比例.
		convenienceFeeRate := rh.calculateTaxRate(orderPayment.GetConvenienceFee(), orderPayment.GetTotalAmount())
		rop.RefundAmount = refundAmount
		rop.RefundConvenienceFee = rh.calculateTaxByRateAndMax(
			rop.GetRefundAmount(),
			convenienceFeeRate,
			orderPayment.GetRefundableConvenienceFee(),
		)

		return decimal.Zero, rop
	}

	// RefundAmount 不包含 ConvenienceFee 的时候.
	// 计算得到的当前 Order Payment 的退的钱是不包含 Convenience Fee 的钱.
	// 需要通过不包含 Convenience Fee 的部分的退款比例乘以 Convenience Fee
	// 得到需要退的 Convenience Fee.

	// Payment 总金额含税，因此这里需要按税计算比例.
	refundRate := rh.calculateTaxRate(
		refundAmount,
		orderPayment.GetTotalAmount().Sub(orderPayment.GetConvenienceFee()),
	)

	rop.RefundConvenienceFee = rh.calculateTaxByRateAndMax(
		orderPayment.GetConvenienceFee(),
		refundRate,
		orderPayment.GetRefundableConvenienceFee(),
	)
	rop.RefundAmount = refundAmount.Add(rop.GetRefundConvenienceFee())

	return decimal.Zero, rop
}

func (*RefundHelper) getOrderPaymentRefundableAmount(
	orderPayment *model.OrderPayment,
	refundAmountFlags *ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags,
) decimal.Decimal {
	if refundAmountFlags.GetIsConvenienceFeeIncluded() {
		return orderPayment.GetRefundableAmount()
	}

	return orderPayment.GetRefundableAmount().Sub(orderPayment.GetRefundableConvenienceFee())
}

type RefundDepositDetail struct {
	// DepositChangeLog for overpaid deposit
	ReversalChangeLog *model.DepositChangeLog
}

func (rh *RefundHelper) GetRefundDepositDetail(
	orderDetail *model.OrderDetail,
	refundOrderItems []*model.RefundOrderItem,
	ignoreChangeLog bool,
) (*RefundDepositDetail, error) {
	ret := &RefundDepositDetail{}

	if ignoreChangeLog ||
		rh.dd == nil || // 非 deposit
		len(refundOrderItems) == 0 || // 没有请求 deposit item 的退款
		rh.dd.LatestChangeLog.Balance.LessThanOrEqual(decimal.Zero) {
		return ret, nil
	}

	depositItem := refundOrderItems[0]

	return &RefundDepositDetail{
		ReversalChangeLog: &model.DepositChangeLog{
			DepositOrderID: orderDetail.Order.ID,
			ChangeType:     orderpb.DepositChangeType_DECREASE,
			Reason:         orderpb.DepositChangeReason_OVERPAYMENT_REVERSAL,
			ChangedAmount:  depositItem.GetRefundAmount(),
			Balance:        rh.dd.LatestChangeLog.Balance.Sub(depositItem.GetRefundAmount()),
			CurrencyCode:   orderDetail.Order.CurrencyCode,
			PreviousLogID:  rh.dd.LatestChangeLog.ID,
			CompanyID:      orderDetail.Order.CompanyID,
			BusinessID:     orderDetail.Order.BusinessID,
			CustomerID:     orderDetail.Order.CustomerID,
			StaffID:        orderDetail.Order.UpdateBy,
		},
	}, nil
}
