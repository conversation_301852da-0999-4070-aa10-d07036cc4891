package helper

import (
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundByItemResult struct {
	RefundOrderDetail *model.RefundOrderDetail
	IsFullyRefund     bool

	RefundableTips           decimal.Decimal
	RefundableConvenienceFee decimal.Decimal
	IsConvenienceFeeOptional bool

	RefundableOrderPayments []*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment
	CanCombineOrderPayments bool
}

func (res *RefundByItemResult) GetRefundOrderDetail() *model.RefundOrderDetail {
	if res == nil {
		return nil
	}

	return res.RefundOrderDetail
}

func (rh *RefundHelper) RefundByItem(
	byItem *ordersvcpb.RefundOrderRequest_RefundByItem,
	originOrderPayments []*ordersvcpb.RefundOrderRequest_OrderPayment,
	ignoreChangeLog bool,
) (*RefundByItemResult, error) {
	if err := rh.validateStateForRefundByItem(); err != nil {
		return nil, err
	}

	idToOrderItem := lo.KeyBy(
		rh.od.OrderItems,
		func(oi *model.OrderItem) int64 { return oi.ID },
	)

	// 1. 计算各个 Item 的退款明细
	refundItems := make([]*model.RefundOrderItem, 0, len(byItem.RefundItems))

	for _, rit := range byItem.RefundItems {
		item, ok := idToOrderItem[rit.OrderItemId]
		if !ok {
			return nil, status.Errorf(codes.InvalidArgument, "not found order item[%d]", item.ID)
		}

		if item.PurchasedQuantity > 0 {
			// 使用 Package 抵扣过的 Item 不能退款.
			// 这里的错误提示会直接展示给用户，内容是 PM & Design 确定的，请勿随意修改.
			return nil, status.Error(codes.InvalidArgument, "items purchased by packages cannot be refunded")
		}

		ri, err := rh.calculateRefundItem(item, rit)
		if err != nil {
			return nil, err
		}

		refundItems = append(refundItems, ri)
	}

	if err := rh.validateDepositForRefundByItem(refundItems); err != nil {
		return nil, err
	}

	// 2. 汇总到 Items 的明细 Refund Order 级别
	refundOrder := rh.initRefundOrder()
	refundOrder.RefundMode = orderpb.RefundMode_REFUND_MODE_BY_ITEM

	for _, rit := range refundItems {
		// RefundItemSubTotal 是 Discount 之前的金额.
		refundOrder.RefundItemSubTotal = refundOrder.GetRefundItemSubTotal().Add(rit.GetRefundAmount())
		refundOrder.RefundDiscountAmount = refundOrder.GetRefundDiscountAmount().Add(rit.GetRefundDiscountAmount())
		refundOrder.RefundTaxAmount = refundOrder.GetRefundTaxAmount().Add(rit.RefundTax.GetAmount())
	}

	// 计算本次需要退的 Tax.
	refundTaxAmount := decimal.Zero
	for _, roi := range refundItems {
		refundTaxAmount = refundTaxAmount.Add(roi.RefundTax.GetAmount())
	}

	refundOrder.RefundTotalAmount = refundOrder.GetRefundItemSubTotal().
		Sub(refundOrder.GetRefundDiscountAmount()).
		Add(refundOrder.GetRefundTaxAmount())

	// FullyRefund: 所有 Items 都退完
	isFullyRefund := true

	for _, orderItem := range rh.applyRefundOrderItems(refundItems) {
		if orderItem.GetRefundedAmount().LessThan(orderItem.GetTotalAmount()) {
			isFullyRefund = false
			break
		}
	}

	// Tips 只在所有 Item 都退完时才全退.
	refundTips := decimal.Zero
	if isFullyRefund {
		refundTips = rh.od.Order.GetTipsAmount()
	}

	refundDepositDetail, err := rh.GetRefundDepositDetail(rh.od, refundItems, ignoreChangeLog)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to refund deposit: %v", err)
	}

	// ConvenienceFee 按分摊到的 OrderPayment 的计算.

	res := &RefundByItemResult{
		RefundOrderDetail: &model.RefundOrderDetail{
			RefundOrder:         refundOrder,
			RefundOrderItems:    refundItems,
			RefundOrderPayments: nil, // 等 TotalAmount 算完再用 OriginOrderPayments 算.

			ReversalDepositChangeLog: refundDepositDetail.ReversalChangeLog,
		},
		IsFullyRefund: isFullyRefund,

		RefundableTips:           refundTips,
		RefundableConvenienceFee: decimal.Zero, // 等 RefundOrderPayments 算出来再聚合.
		IsConvenienceFeeOptional: false,        // 目前 ConvenienceFee 都必须退，不可选.
		RefundableOrderPayments:  nil,          // 等 TotalAmount 算完再更新
		CanCombineOrderPayments:  false,        // 等 TotalAmount 算完再更新
	}

	// 汇总 Tips 的退款情况到 RefundOrder.
	refundOrder.RefundTipsAmount = res.RefundableTips
	refundOrder.RefundTotalAmount = refundOrder.GetRefundTotalAmount().Add(refundOrder.GetRefundTipsAmount())
	refundOrder.RefundConvenienceFee = res.RefundableConvenienceFee
	refundOrder.RefundTotalAmount = refundOrder.GetRefundTotalAmount().Add(refundOrder.GetRefundConvenienceFee())

	// Sales 单金额不足，尝试退到 deposit
	// 注意这里的 refundOrder.RefundTotalAmount 还没有算出 convenience fee，所以要用减掉 fee 之前的金额比较
	refundableItemAmount := rh.od.Order.GetPaidAmount().Sub(rh.od.Order.GetConvenienceFee())

	for _, rod := range rh.rods {
		refundedItemAmount := rod.RefundOrder.GetRefundTotalAmount().Sub(rod.RefundOrder.GetRefundConvenienceFee())
		refundableItemAmount = refundableItemAmount.Sub(refundedItemAmount)
	}

	if refundableItemAmount.LessThan(refundOrder.GetRefundTotalAmount()) {
		refundByDepositAmount := refundOrder.GetRefundTotalAmount().Sub(refundableItemAmount)
		if rh.od.Order.DepositAmount.GreaterThanOrEqual(refundByDepositAmount) {
			refundOrder.RefundDepositAmount = refundByDepositAmount
			refundOrder.RefundTotalAmount = refundableItemAmount
		}
	}

	// 通过订单的 PaidAmount 快速检查是否能足够退钱.
	if rh.od.Order.GetRefundableAmount().LessThan(refundOrder.GetRefundTotalAmount()) {
		return nil, status.Error(codes.InvalidArgument, "refundable amount is not enough")
	}

	// 更新 RefundablePayment
	refundableOrderPayments, canCombineOrderPayments, err := rh.BuildRefundablePayments(
		rh.od.OrderPayments,
		refundOrder.GetRefundTotalAmount(),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: false, // Item 金额本身不包含 Convenience Fee.
		},
	)
	if err != nil {
		return nil, err
	}

	res.RefundableOrderPayments = refundableOrderPayments
	res.CanCombineOrderPayments = canCombineOrderPayments

	// 更新 OrderPayments
	refundOrderPayments, err := rh.PreviewRefundOrderPayments(
		refundableOrderPayments,
		lo.Map(
			originOrderPayments,
			func(it *ordersvcpb.RefundOrderRequest_OrderPayment, _ int) int64 { return it.GetId() },
		),
		// 此时 RefundTotalAmount 应当除了 ConvenienceFee 之外所有的金额都已经计入.
		refundOrder.GetRefundTotalAmount(),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: false, // 如上一个参数的说明，此时未包含 Convenience Fee.
		},
	)
	if err != nil {
		return nil, err
	}

	res.RefundOrderDetail.RefundOrderPayments = refundOrderPayments

	for _, rop := range refundOrderPayments {
		res.RefundableConvenienceFee = res.RefundableConvenienceFee.Add(rop.GetRefundConvenienceFee())
	}

	// 更新 Refund Convenience Fee.
	refundOrder.RefundConvenienceFee = res.RefundableConvenienceFee
	refundOrder.RefundTotalAmount = refundOrder.GetRefundTotalAmount().Add(refundOrder.GetRefundConvenienceFee())

	return res, nil
}

// validateStateForRefundByItem 检查当前的 order、refund order 状态知否允许 refund by item
func (rh *RefundHelper) validateStateForRefundByItem() error {
	if !rh.od.IsComplete() {
		// 未关单的订单只能 Refund By Payment
		return status.Error(codes.FailedPrecondition, "order is not completed")
	}

	if rh.od.OrderVersion().Lt(model.OrderVersionRefund) {
		// 老版本只支持 RefundByPayment
		return status.Error(codes.InvalidArgument, "only support new order")
	}

	// 关单后只能使用一种退款模式.
	refundOrder, found := lo.Find(
		rh.rods,
		func(it *model.RefundOrderDetail) bool {
			return it.IsCreatedAfterCompleted() &&
				it.RefundOrder.RefundMode != orderpb.RefundMode_REFUND_MODE_BY_ITEM
		},
	)

	if !found {
		// 关单后没有退过款.
		return nil
	}

	return status.Errorf(
		codes.InvalidArgument,
		"found refunded order[%d] with another mode[%s]",
		refundOrder.RefundOrder.ID,
		refundOrder.RefundOrder.RefundMode,
	)
}

// validateDepositForRefundByItem 检查退款请求的 refund items 参数是否合法
func (rh *RefundHelper) validateDepositForRefundByItem(refundOrderItems []*model.RefundOrderItem) error {
	if rh.od.Order.OrderType != orderpb.OrderModel_DEPOSIT || len(refundOrderItems) == 0 {
		return nil
	}

	if refundOrderItems[0].ItemType != model.OrderItemTypeDeposit {
		return status.Error(codes.InvalidArgument, "deposit item must be included as the first item")
	}

	if rh.dd.LatestChangeLog.Balance.IsPositive() &&
		rh.dd.LatestChangeLog.Balance.LessThan(refundOrderItems[0].GetRefundAmount()) {
		return status.Error(codes.InvalidArgument, "requested refund amount exceeds unused deposit balance")
	}

	return nil
}

func (rh *RefundHelper) calculateRefundItem(
	item *model.OrderItem,
	refundItem *ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem,
) (*model.RefundOrderItem, error) {
	switch refundItem.RefundItemMode {
	case orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT:
		return rh.calculateRefundItemByAmount(item, refundItem)

	case orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY:
		return rh.calculateRefundItemByQuantity(item, refundItem)

	default:
		return nil, status.Error(codes.InvalidArgument, "unsupported refund item mode")
	}
}

func (rh *RefundHelper) calculateRefundItemByAmount(
	item *model.OrderItem,
	refundItem *ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem,
) (*model.RefundOrderItem, error) {
	refundTotalAmount := money.ToDecimal(refundItem.GetRefundAmount())
	refundableTotalAmount := item.GetTotalAmount().Sub(item.GetRefundedAmount())
	refundableTax := item.Tax.GetAmount().Sub(item.GetRefundedTaxAmount())
	refundableDiscount := item.GetDiscountAmount().Sub(item.GetRefundedDiscountAmount())

	switch {
	case refundTotalAmount.GreaterThan(refundableTotalAmount):
		return nil, status.Error(codes.InvalidArgument, "refundable amount is not enough")

	case refundTotalAmount.Equal(refundableTotalAmount):
		// Fully refund.
		roi := rh.initRefundOrderItem(item)
		roi.RefundItemMode = orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT
		roi.RefundQuantity = 0 // ByAmount 时固定为 0.
		roi.RefundDiscountAmount = refundableDiscount
		roi.RefundTax.Amount = refundableTax

		// 用户输入的值实为税前价格，即： subtotal - discount 的
		// 因此输入的是 refundTotalAmount
		roi.RefundTotalAmount = refundableTotalAmount
		// 倒算 RefundAmount （即 subtotal 的部分）
		roi.RefundAmount = roi.GetRefundTotalAmount().Add(roi.GetRefundDiscountAmount())

		return roi, nil

	default:
		// Partial refund.
		// 这里的金额不包含 Tax，按照非 Tax 计算.
		refundRate := rh.calculateRate(refundTotalAmount, item.TotalAmount)
		roi := rh.initRefundOrderItem(item)
		roi.RefundItemMode = orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT
		roi.RefundQuantity = 0 // ByAmount 时固定为 0.
		roi.RefundDiscountAmount = rh.calculateByRateAndMax(
			item.GetDiscountAmount(), refundRate, refundableDiscount,
		)
		// 用户输入的值实为税前价格，即： subtotal - discount 的
		// 因此输入的是 refundTotalAmount
		roi.RefundTotalAmount = refundTotalAmount
		// 倒算 RefundAmount （即 subtotal 的部分）
		roi.RefundAmount = roi.GetRefundTotalAmount().Add(roi.GetRefundDiscountAmount())
		// 退的税按照退款之后的金额重新正向计算，然后与收取的 Tax 做差得到本次退的 Tax.
		restTotalAmount := item.TotalAmount.Sub(roi.RefundTotalAmount).Sub(item.RefundedAmount)
		newTaxAmount := rh.calculateTaxByRateAndMax(
			restTotalAmount,
			item.Tax.Rate.Shift(-2).Round(RatePrecision),
			restTotalAmount,
		)
		roi.RefundTax.Amount = item.Tax.Amount.Sub(newTaxAmount).Sub(item.RefundedTaxAmount)

		return roi, nil
	}
}

func (rh *RefundHelper) calculateRefundItemByQuantity(
	item *model.OrderItem,
	refundItem *ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem,
) (*model.RefundOrderItem, error) {
	refundableQuantity := item.Quantity - item.RefundedQuantity - item.PurchasedQuantity
	refundableTotalAmount := item.GetTotalAmount().Sub(item.GetRefundedAmount())
	refundableTax := item.Tax.GetAmount().Sub(item.GetRefundedTaxAmount())
	refundableDiscount := item.GetDiscountAmount().Sub(item.GetRefundedDiscountAmount())

	switch {
	case refundItem.GetRefundQuantity() > int64(refundableQuantity):
		return nil, status.Error(codes.InvalidArgument, "refundable quantity is not enough")

	case refundItem.GetRefundQuantity() == int64(refundableQuantity):
		// Fully refund.
		// 轧差.
		roi := rh.initRefundOrderItem(item)
		roi.RefundItemMode = orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY
		roi.RefundQuantity = refundableQuantity
		roi.RefundAmount = roi.GetItemUnitPrice().Mul(decimal.NewFromInt32(roi.RefundQuantity))
		roi.RefundDiscountAmount = refundableDiscount
		roi.RefundTax.Amount = refundableTax
		roi.RefundTotalAmount = refundableTotalAmount
		// 倒算 RefundAmount （即 subtotal 的部分）
		roi.RefundAmount = roi.GetRefundTotalAmount().Add(roi.GetRefundDiscountAmount())

		return roi, nil

	default:
		// Partial refund.
		// 按比例计算.
		refundRate := decimal.NewFromInt(refundItem.GetRefundQuantity()).
			DivRound(decimal.NewFromInt32(item.Quantity), RatePrecision)

		roi := rh.initRefundOrderItem(item)
		roi.RefundItemMode = orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY
		roi.RefundQuantity = int32(refundItem.GetRefundQuantity()) //nolint:gosec // 数量是 int32
		roi.RefundAmount = roi.GetItemUnitPrice().Mul(decimal.NewFromInt32(roi.RefundQuantity))
		roi.RefundDiscountAmount = rh.calculateByRateAndMax(
			item.GetDiscountAmount(), refundRate, refundableDiscount,
		)
		roi.RefundTax.Amount = rh.calculateTaxByRateAndMax(item.Tax.GetAmount(), refundRate, refundableTax)
		roi.RefundTotalAmount = roi.GetRefundAmount().Sub(roi.GetRefundDiscountAmount())

		return roi, nil
	}
}

func (rh *RefundHelper) applyRefundOrderItems(refundOrderItems []*model.RefundOrderItem) []*model.OrderItem {
	newOrderItems := lo.Map(
		rh.od.OrderItems,
		func(oi *model.OrderItem, _ int) *model.OrderItem { return oi.Clone() },
	)

	orderItemIDToRefundItem := lo.KeyBy(
		refundOrderItems,
		func(ri *model.RefundOrderItem) int64 { return ri.OrderItemID },
	)

	for _, oi := range newOrderItems {
		ri, ok := orderItemIDToRefundItem[oi.ID]
		if !ok {
			// 本次没有退该 Item
			continue
		}

		oi.RefundedQuantity += ri.RefundQuantity
		oi.RefundedAmount = oi.GetRefundedAmount().Add(ri.GetRefundTotalAmount())
		oi.RefundedDiscountAmount = oi.GetRefundedDiscountAmount().Add(ri.GetRefundDiscountAmount())
		oi.RefundedTaxAmount = oi.GetRefundedTaxAmount().Add(ri.RefundTax.GetAmount())
	}

	return newOrderItems
}
