package helper

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

type RefundByItemTestSuite struct {
	suite.Suite
}

func TestRefundByItem(t *testing.T) {
	suite.Run(t, new(RefundByItemTestSuite))
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_ByAmount_Partial() {
	// 首次部分退.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.78"),
		},
		Quantity:               1,
		TipsAmount:             ts.mustDecimal("15.00"),
		DiscountAmount:         decimal.Zero,
		SubTotalAmount:         ts.mustDecimal("150.00"),
		TotalAmount:            ts.mustDecimal("150.00"),
		RefundedAmount:         decimal.Zero,
		RefundedTaxAmount:      decimal.Zero,
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
			RefundAmount: money.FromDecimal(ts.mustDecimal("10.00"), ""),
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("0.39"),
		},
		RefundTotalAmount:    ts.mustDecimal("10.00"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("10.00"),
		RefundDiscountAmount: decimal.Zero,
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_ByAmount_Partial_After_Partial() {
	// 已经部分退过一次的基础上，继续部分退.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.78"),
		},
		Quantity:               1,
		TipsAmount:             ts.mustDecimal("15.00"),
		DiscountAmount:         decimal.Zero,
		SubTotalAmount:         ts.mustDecimal("150.00"),
		TotalAmount:            ts.mustDecimal("150.00"),
		RefundedAmount:         ts.mustDecimal("10.00"),
		RefundedTaxAmount:      ts.mustDecimal("0.39"),
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
			RefundAmount: money.FromDecimal(ts.mustDecimal("10.00"), ""),
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate: ts.mustDecimal("3.8500"),
			// 5.78 - ((150 - 10 - 10) * 3.85%) - 0.39
			//         Tax 按照四舍五入计算得 5.01
			// 5.78 - 5.01 - 0.39 = 0.38
			Amount: ts.mustDecimal("0.38"),
		},
		RefundTotalAmount:    ts.mustDecimal("10.00"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("10.00"),
		RefundDiscountAmount: decimal.Zero,
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_ByAmount_Full_After_Partial() {
	// 已经部分退过一次的基础上，全退.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.78"),
		},
		Quantity:               1,
		TipsAmount:             ts.mustDecimal("15.00"),
		DiscountAmount:         decimal.Zero,
		SubTotalAmount:         ts.mustDecimal("150.00"),
		TotalAmount:            ts.mustDecimal("150.00"),
		RefundedAmount:         ts.mustDecimal("10.00"),
		RefundedTaxAmount:      ts.mustDecimal("0.39"),
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
			RefundAmount: money.FromDecimal(ts.mustDecimal("140.00"), ""),
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.39"),
		},
		RefundTotalAmount:    ts.mustDecimal("140.00"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("140.00"),
		RefundDiscountAmount: decimal.Zero,
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_ByAmount_Full() {
	// 首次全退.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.78"),
		},
		Quantity:               1,
		TipsAmount:             ts.mustDecimal("15.00"),
		DiscountAmount:         decimal.Zero,
		SubTotalAmount:         ts.mustDecimal("150.00"),
		TotalAmount:            ts.mustDecimal("150.00"),
		RefundedAmount:         decimal.Zero,
		RefundedTaxAmount:      decimal.Zero,
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
			RefundAmount: money.FromDecimal(ts.mustDecimal("150.00"), ""),
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.78"),
		},
		RefundTotalAmount:    ts.mustDecimal("150.00"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("150.00"),
		RefundDiscountAmount: decimal.Zero,
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_ByQuantity_Partial() {
	// 首次部分退.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.78"),
		},
		Quantity:               2,
		TipsAmount:             ts.mustDecimal("15.00"),
		UnitPrice:              ts.mustDecimal("75.00"),
		DiscountAmount:         decimal.Zero,
		SubTotalAmount:         ts.mustDecimal("150.00"),
		TotalAmount:            ts.mustDecimal("150.00"),
		RefundedAmount:         decimal.Zero,
		RefundedTaxAmount:      decimal.Zero,
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity{
			RefundQuantity: 1,
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
		RefundTax: model.RefundTax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("2.89"),
		},
		RefundTotalAmount:    ts.mustDecimal("75.00"),
		RefundQuantity:       1,
		RefundAmount:         ts.mustDecimal("75.00"),
		RefundDiscountAmount: decimal.Zero,
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_ByQuantity_Partial_After_Partial() {
	// 部分退之后部分退.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("8.66"),
		},
		Quantity:               3,
		RefundedQuantity:       1, // 之前已退 1 个.
		TipsAmount:             ts.mustDecimal("15.00"),
		UnitPrice:              ts.mustDecimal("75.00"),
		DiscountAmount:         decimal.Zero,
		SubTotalAmount:         ts.mustDecimal("225.00"),
		TotalAmount:            ts.mustDecimal("225.00"),
		RefundedAmount:         ts.mustDecimal("75.00"),
		RefundedTaxAmount:      ts.mustDecimal("2.88"),
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity{
			RefundQuantity: 1,
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
		RefundTax: model.RefundTax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("2.89"),
		},
		RefundTotalAmount:    ts.mustDecimal("75.00"),
		RefundQuantity:       1,
		RefundAmount:         ts.mustDecimal("75.00"),
		RefundDiscountAmount: decimal.Zero,
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_ByQuantity_Full_After_Partial() {
	// 部分退之后全退.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("8.66"),
		},
		Quantity:               3,
		RefundedQuantity:       1, // 之前已退 1 个.
		TipsAmount:             ts.mustDecimal("15.00"),
		UnitPrice:              ts.mustDecimal("75.00"),
		DiscountAmount:         decimal.Zero,
		SubTotalAmount:         ts.mustDecimal("225.00"),
		TotalAmount:            ts.mustDecimal("225.00"),
		RefundedAmount:         ts.mustDecimal("75.00"),
		RefundedTaxAmount:      ts.mustDecimal("2.88"),
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity{
			RefundQuantity: 2,
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
		RefundTax: model.RefundTax{
			Rate:   ts.mustDecimal("3.8500"),
			Amount: ts.mustDecimal("5.78"),
		},
		RefundTotalAmount:    ts.mustDecimal("150.00"),
		RefundQuantity:       2,
		RefundAmount:         ts.mustDecimal("150.00"),
		RefundDiscountAmount: decimal.Zero,
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_HUAZI_001() {
	// 来自 QA 反馈的 BUG case.
	item := &model.OrderItem{
		Tax: model.Tax{
			Rate:   ts.mustDecimal("0.0000"),
			Amount: ts.mustDecimal("0.00"),
		},
		Quantity:               1,
		TipsAmount:             ts.mustDecimal("10.00"),
		DiscountAmount:         ts.mustDecimal("0.30"),
		SubTotalAmount:         ts.mustDecimal("10.00"),
		TotalAmount:            ts.mustDecimal("9.70"),
		RefundedAmount:         decimal.Zero,
		RefundedTaxAmount:      decimal.Zero,
		RefundedDiscountAmount: decimal.Zero,
	}
	refundItem := &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
			RefundAmount: money.FromDecimal(ts.mustDecimal("9.70"), ""),
		},
	}

	expected := &model.RefundOrderItem{
		RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
		RefundTax: model.RefundTax{
			Rate:   ts.mustDecimal("0.0000"),
			Amount: ts.mustDecimal("0.00"),
		},
		RefundTotalAmount:    ts.mustDecimal("9.70"),
		RefundQuantity:       0,
		RefundAmount:         ts.mustDecimal("10.00"),
		RefundDiscountAmount: ts.mustDecimal("0.30"),
	}

	rh := &RefundHelper{od: &model.OrderDetail{Order: &model.Order{}}}

	actual, err := rh.calculateRefundItem(item, refundItem)
	ts.NoError(err)
	equalRefundOrderItem(ts.Assert(), expected, actual)
}

func (ts *RefundByItemTestSuite) TestCalculateRefundItem_HUAZI_002() {
	const paymentID = 1

	rh := RefundHelper{
		od: &model.OrderDetail{
			Order: &model.Order{},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     paymentID,
					TotalAmount:            ts.mustDecimal("81.25"),
					Amount:                 ts.mustDecimal("78.19"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("3.06"),
					ConvenienceFee:         ts.mustDecimal("3.06"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		},
	}

	rop, err := rh.PreviewRefundOrderPayments(
		[]*ordersvcpb.PreviewRefundOrderResponse_RefundableOrderPayment{
			{
				OrderPaymentId:   paymentID,
				RefundableAmount: money.FromDecimal(ts.mustDecimal("81.25"), ""),
			},
		},
		[]int64{paymentID},
		ts.mustDecimal("10.00"),
		&ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{IsConvenienceFeeIncluded: false},
	)
	ts.NoError(err)
	ts.Len(rop, 1)
	ts.Equal(ts.mustDecimal("10.39").String(), rop[0].GetRefundAmount().String())
	ts.Equal(ts.mustDecimal("0.39").String(), rop[0].GetRefundConvenienceFee().String())
}

func (ts *RefundByItemTestSuite) TestRefundByItem_Partial() {
	rh := NewRefundHelper(
		&model.OrderDetail{
			Order: &model.Order{
				Status:          orderpb.OrderStatus_COMPLETED,
				OrderVersion:    model.OrderVersionRefund,
				CurrencyCode:    "USD",
				TipsAmount:      ts.mustDecimal("0.00"),
				TaxAmount:       ts.mustDecimal("11.56"),
				DiscountAmount:  ts.mustDecimal("0.00"),
				ConvenienceFee:  decimal.Zero,
				SubTotalAmount:  ts.mustDecimal("300.00"),
				TipsBasedAmount: decimal.Zero,
				TotalAmount:     ts.mustDecimal("311.56"),
				PaidAmount:      ts.mustDecimal("311.56"),
				RemainAmount:    decimal.Zero,
				RefundedAmount:  decimal.Zero,
			},
			OrderItems: []*model.OrderItem{
				{
					ID: 11,
					Tax: model.Tax{
						Rate:   ts.mustDecimal("3.8500"),
						Amount: ts.mustDecimal("5.78"),
					},
					Quantity:               2,
					UnitPrice:              ts.mustDecimal("80.00"),
					TipsAmount:             ts.mustDecimal("15.00"),
					DiscountAmount:         ts.mustDecimal("10.00"),
					SubTotalAmount:         ts.mustDecimal("160.00"),
					TotalAmount:            ts.mustDecimal("150.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
				{
					ID: 12,
					Tax: model.Tax{
						Rate:   ts.mustDecimal("3.8500"),
						Amount: ts.mustDecimal("5.78"),
					},
					Quantity:               1,
					UnitPrice:              ts.mustDecimal("200.00"),
					TipsAmount:             ts.mustDecimal("0.00"),
					DiscountAmount:         ts.mustDecimal("50.00"),
					SubTotalAmount:         ts.mustDecimal("200.00"),
					TotalAmount:            ts.mustDecimal("150.00"),
					RefundedAmount:         decimal.Zero,
					RefundedTaxAmount:      decimal.Zero,
					RefundedDiscountAmount: decimal.Zero,
				},
			},
			OrderPayments: []*model.OrderPayment{
				{
					ID:                     21,
					TotalAmount:            ts.mustDecimal("311.56"),
					Amount:                 ts.mustDecimal("311.56"),
					RefundedAmount:         decimal.Zero,
					ProcessingFee:          ts.mustDecimal("0.00"),
					ConvenienceFee:         ts.mustDecimal("0.00"),
					RefundedConvenienceFee: decimal.Zero,
					PaymentStatus:          orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,
				},
			},
		}, nil, nil,
	)

	actual, err := rh.RefundByItem(
		&ordersvcpb.RefundOrderRequest_RefundByItem{
			RefundItems: []*ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem{
				{
					OrderItemId:         11,
					RefundItemMode:      orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
					RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity{RefundQuantity: 1},
				}, {
					OrderItemId:    12,
					RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
					RefundItemModeParam: &ordersvcpb.RefundOrderRequest_RefundByItem_RefundItem_RefundAmount{
						RefundAmount: money.FromDecimal(ts.mustDecimal("50.00"), ""),
					},
				},
			},
		},
		[]*ordersvcpb.RefundOrderRequest_OrderPayment{
			{
				Id: 21,
			},
		},
		false,
	)

	expectedItems := []*model.RefundOrderItem{
		{
			ID:             11,
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_QUANTITY,
			RefundTax: model.RefundTax{
				Rate:   ts.mustDecimal("3.8500"),
				Amount: ts.mustDecimal("2.89"),
			},
			RefundTotalAmount:    ts.mustDecimal("75.00"),
			RefundQuantity:       1,
			RefundAmount:         ts.mustDecimal("80.00"),
			RefundDiscountAmount: ts.mustDecimal("5.00"),
		},
		{
			ID:             12,
			RefundItemMode: orderpb.RefundItemMode_REFUND_ITEM_MODE_BY_AMOUNT,
			RefundTax: model.RefundTax{
				Rate:   ts.mustDecimal("3.8500"),
				Amount: ts.mustDecimal("1.93"),
			},
			RefundTotalAmount:    ts.mustDecimal("50.00"),
			RefundQuantity:       0,
			RefundAmount:         ts.mustDecimal("66.67"),
			RefundDiscountAmount: ts.mustDecimal("16.67"),
		},
	}

	ts.Require().NoError(err)
	ts.Require().False(actual.IsFullyRefund)
	ts.Require().Len(actual.RefundOrderDetail.RefundOrderItems, len(expectedItems))

	for idx, expected := range expectedItems {
		actualItem := actual.RefundOrderDetail.RefundOrderItems[idx]
		equalRefundOrderItem(ts.Assert(), expected, actualItem)
	}

	expectedPayments := []*model.RefundOrderPayment{
		{
			OrderPaymentID:       21,
			CurrencyCode:         "",
			RefundAmount:         ts.mustDecimal("129.82"),
			RefundConvenienceFee: ts.mustDecimal("0.00"),
		},
	}

	ts.Require().Len(actual.RefundOrderDetail.RefundOrderPayments, len(expectedPayments))

	for idx, expected := range expectedPayments {
		actualPayment := actual.RefundOrderDetail.RefundOrderPayments[idx]
		equalRefundOrderPayment(ts.Assert(), expected, actualPayment)
	}

	expectedRefundOrder := &model.RefundOrder{
		RefundMode:           orderpb.RefundMode_REFUND_MODE_BY_ITEM,
		CurrencyCode:         "USD",
		RefundTotalAmount:    ts.mustDecimal("129.82"),
		RefundItemSubTotal:   ts.mustDecimal("146.67"),
		RefundDiscountAmount: ts.mustDecimal("21.67"),
		RefundTipsAmount:     ts.mustDecimal("0.00"),
		RefundConvenienceFee: ts.mustDecimal("0.00"),
		RefundTaxAmount:      ts.mustDecimal("4.82"),
	}
	equalRefundOrder(ts.Assert(), expectedRefundOrder, actual.RefundOrderDetail.RefundOrder)
}

func (ts *RefundByItemTestSuite) mustDecimal(str string) decimal.Decimal {
	dec, err := decimal.NewFromString(str)
	ts.Require().NoError(err)

	return dec
}
