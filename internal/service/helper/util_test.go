package helper

import (
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
)

func equalRefundOrderItem(ast *assert.Assertions, expected, actual *model.RefundOrderItem) {
	ast.Equal(expected.RefundItemMode, actual.RefundItemMode)
	ast.Equal(expected.RefundTax.Rate.String(), actual.RefundTax.Rate.String())
	ast.Equal(expected.RefundTax.Amount.String(), actual.RefundTax.Amount.String())
	ast.Equal(expected.RefundTotalAmount.String(), actual.RefundTotalAmount.String())
	ast.Equal(expected.RefundQuantity, actual.RefundQuantity)
	ast.Equal(expected.RefundAmount.String(), actual.RefundAmount.String())
	ast.Equal(expected.RefundDiscountAmount.String(), actual.RefundDiscountAmount.String())
}

func equalRefundOrderPayment(ast *assert.Assertions, expected, actual *model.RefundOrderPayment) {
	ast.Equal(expected.OrderPaymentID, actual.OrderPaymentID)
	ast.Equal(expected.CurrencyCode, actual.CurrencyCode)
	ast.Equal(expected.RefundAmount.String(), actual.RefundAmount.String())
	ast.Equal(expected.RefundConvenienceFee.String(), actual.RefundConvenienceFee.String())
}

func equalRefundOrder(ast *assert.Assertions, expected, actual *model.RefundOrder) {
	ast.Equal(expected.RefundMode, actual.RefundMode)
	ast.Equal(expected.CurrencyCode, actual.CurrencyCode)
	ast.Equal(expected.RefundTotalAmount.String(), actual.RefundTotalAmount.String())
	ast.Equal(expected.RefundItemSubTotal.String(), actual.RefundItemSubTotal.String())
	ast.Equal(expected.RefundDiscountAmount.String(), actual.RefundDiscountAmount.String())
	ast.Equal(expected.RefundTipsAmount.String(), actual.RefundTipsAmount.String())
	ast.Equal(expected.RefundConvenienceFee.String(), actual.RefundConvenienceFee.String())
	ast.Equal(expected.RefundTaxAmount.String(), actual.RefundTaxAmount.String())
}
