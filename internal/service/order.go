package service

import (
	"context"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/zlog"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/core"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
)

type OrderService interface {
	GetOrder(ctx context.Context, orderID int64) (*model.Order, error)
	GetRootOrderBySource(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) (*model.Order, error)
	GetDetail(ctx context.Context, orderID int64) (*model.OrderDetail, error)
	ListTail(ctx context.Context, orderID int64) ([]*model.Order, error)
	BatchGetOrders(ctx context.Context, orderID []int64) ([]*model.Order, error)
	ListTailDetail(ctx context.Context, orderID int64) ([]*model.OrderDetail, error)

	ListByAppointment(ctx context.Context, appointmentID int64) ([]*model.Order, error)
	ListDetailByAppointment(ctx context.Context, appointmentID int64) ([]*model.OrderDetail, error)

	ListDetailByFulfillment(ctx context.Context, fulfillmentID int64) ([]*model.OrderDetail, error)

	CreateOrder(ctx context.Context, orderDetail *model.OrderDetail) (*model.OrderDetail, error)
	CreateNoShowOrder(ctx context.Context, req *ordersvcpb.CreateNoShowOrderRequest) (*model.OrderDetail, error)
	CreateTipOrder(ctx context.Context, req *ordersvcpb.CreateTipOrderRequest) (*model.OrderDetail, error)
	CancelOrder(ctx context.Context, orderID int64, cancelReason string) error

	// CreateInvoiceID 用途是在 check in 的时候，获取一个稳定的 invoice id.
	// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
	CreateInvoiceID(ctx context.Context, req *ordersvcpb.CreateInvoiceIDRequest) (int64, error)

	AttachDetail(ctx context.Context, order *model.Order) (*model.OrderDetail, error)
}

func NewOrderService(
	orderRepo repo.OrderRepo,
	orderItemRepo repo.OrderItemRepo,
	orderPaymentRepo repo.OrderPaymentRepo,
	orderLineDiscountRepo repo.OrderLineDiscountRepo,
	refundOrderPaymentRepo repo.RefundOrderPaymentRepo,
	refundOrderRepo repo.RefundOrderRepo,
	depositChangeLogRepo repo.DepositChangeLogRepo,
	orderPromotionRepo repo.OrderPromotionRepo,
	orderPromotionItemRepo repo.OrderPromotionItemRepo,

	txRepo repo.TXRepo,

	userFlagCli repo.UserFlagClient,

	legacyPaymentRepo repo.LegacyPaymentRepo,
	legacyRefundRepo repo.LegacyRefundRepo,
	groomingCli grooming.Client,
	paymentClient repo.PaymentClient,
) OrderService {
	return &orderService{
		orderRepo:              orderRepo,
		orderItemRepo:          orderItemRepo,
		orderPaymentRepo:       orderPaymentRepo,
		orderLineDiscountRepo:  orderLineDiscountRepo,
		refundOrderRepo:        refundOrderRepo,
		refundOrderPaymentRepo: refundOrderPaymentRepo,
		depositChangeLogRepo:   depositChangeLogRepo,
		orderPromotionRepo:     orderPromotionRepo,
		orderPromotionItemRepo: orderPromotionItemRepo,

		txRepo: txRepo,

		userFlagCli: userFlagCli,

		legacyPaymentRepo: legacyPaymentRepo,
		legacyRefundRepo:  legacyRefundRepo,
		groomingClient:    groomingCli,
		paymentClient:     paymentClient,
	}
}

type orderService struct {
	orderRepo             repo.OrderRepo
	orderItemRepo         repo.OrderItemRepo
	orderPaymentRepo      repo.OrderPaymentRepo
	orderLineDiscountRepo repo.OrderLineDiscountRepo

	refundOrderRepo        repo.RefundOrderRepo
	refundOrderPaymentRepo repo.RefundOrderPaymentRepo

	depositChangeLogRepo repo.DepositChangeLogRepo

	orderPromotionRepo     repo.OrderPromotionRepo
	orderPromotionItemRepo repo.OrderPromotionItemRepo

	txRepo repo.TXRepo

	userFlagCli repo.UserFlagClient

	legacyPaymentRepo repo.LegacyPaymentRepo
	legacyRefundRepo  repo.LegacyRefundRepo

	groomingClient grooming.Client
	paymentClient  repo.PaymentClient
}

// CreateTipOrder implements OrderService.
func (svc *orderService) CreateTipOrder(
	ctx context.Context,
	req *ordersvcpb.CreateTipOrderRequest,
) (*model.OrderDetail, error) {
	// 检查 source id and type 是否匹配
	if req.GetSourceType() != orderpb.OrderSourceType_APPOINTMENT {
		return nil, status.Errorf(codes.InvalidArgument, "source type must be appointment")
	}

	// 检查 Business 是否在 NewInvoice 白名单中.
	switched, err := svc.userFlagCli.SwitchedToNewInvoice(ctx, req.GetBusinessId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to check user flag: %v", err)
	}

	orderVersion := model.OrderVersionLegacy
	if switched {
		orderVersion = model.OrderVersionStableLatest
	}

	amount := money.ToDecimal(req.GetTipAmount())

	tipOrder := &model.Order{
		OrderVersion:      orderVersion,
		CompanyID:         req.GetCompanyId(),
		BusinessID:        req.GetBusinessId(),
		CustomerID:        req.GetCustomerId(),
		CreateBy:          req.GetStaffId(),
		UpdateBy:          req.GetStaffId(),
		GUID:              "",
		Title:             "Tip order", // 固定值
		Description:       req.GetDescription(),
		Status:            orderpb.OrderStatus_CREATED,
		FulfillmentStatus: orderpb.OrderModel_COMPLETED.String(),
		PaymentStatus:     orderpb.OrderModel_UNPAID,
		OrderType:         orderpb.OrderModel_TIP,
		OrderRefID:        0, // Fill below.
		ExtraChargeReason: "",
		// 这里因为历史原因，得先用这个。后续统一调整成 PB 定义的 Enum.
		SourceType:      "appointment",
		SourceID:        req.GetSourceId(),
		Version:         0,
		TaxRoundMode:    model.TaxRoundModeHalfUp, // 新单都是四舍五入.
		CurrencyCode:    req.GetTipAmount().GetCurrencyCode(),
		LineItemTypes:   0,
		TipsAmount:      amount,
		TaxAmount:       decimal.Zero,
		DiscountAmount:  decimal.Zero,
		ConvenienceFee:  decimal.Zero,
		SubTotalAmount:  decimal.Zero,
		TipsBasedAmount: money.ToDecimal(req.TipBasedAmount),
		TotalAmount:     amount,
		PaidAmount:      decimal.Zero,
		RemainAmount:    amount,
		RefundedAmount:  decimal.Zero,
	}

	txErr := svc.txRepo.Tx(
		func(tx repo.OrderTX) error {
			// Lock the related orders for paralellel update/create
			orders, err := tx.Order().ListByAppointmentForUpdate(ctx, req.GetSourceId())
			if err != nil {
				return err
			}

			// 给 tip order 也挂上 Ref ID，便于 ListOrders 可以查到原单.
			tipOrder.OrderRefID = svc.getOrderRefID(ctx, orders)

			// Check if there is an incomplete tip order, update if exists, otherwise create a new order
			existingUncompletedTipOrder, found := lo.Find(
				orders, func(o *model.Order) bool {
					return o.IsTip() && !o.IsFinalStatus()
				},
			)
			if found {
				// Update the existing tip order instead of creating a new one
				// tips order already have the newest tips_amount,total_amount
				// now only update paid_amount and remain_amount
				tipOrder.ID = existingUncompletedTipOrder.ID
				tipOrder.PaidAmount = existingUncompletedTipOrder.PaidAmount
				tipOrder.RemainAmount = tipOrder.TotalAmount.Sub(existingUncompletedTipOrder.PaidAmount)

				if _, updateErr := tx.Order().ResetTipAmount(ctx, tipOrder); updateErr != nil {
					return updateErr
				}
				// Use the existing order
				*tipOrder = *existingUncompletedTipOrder

				return nil
			}

			// No existing unpaid tip order found, create a new one
			if err := tx.Order().Create(ctx, tipOrder); err != nil {
				return err
			}

			// no item for tip order
			return nil
		},
	)
	if txErr != nil {
		return nil, txErr
	}

	orderDetail := &model.OrderDetail{
		Order: tipOrder,
	}

	return orderDetail, nil
}

func (svc *orderService) GetOrder(ctx context.Context, orderID int64) (*model.Order, error) {
	order, err := svc.orderRepo.Get(ctx, orderID)
	if err != nil {
		return nil, err
	}

	return order, nil
}

func (svc *orderService) GetRootOrderBySource(
	ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64,
) (*model.Order, error) {
	order, err := svc.orderRepo.GetRootBySource(ctx, sourceType, sourceID)
	if err != nil {
		return nil, err
	}

	return order, nil
}

func (svc *orderService) BatchGetOrders(ctx context.Context, orderIDs []int64) ([]*model.Order, error) {
	orders, err := svc.orderRepo.BatchGetOrders(ctx, orderIDs)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (svc *orderService) GetDetail(ctx context.Context, orderID int64) (*model.OrderDetail, error) {
	order, err := svc.GetOrder(ctx, orderID)
	if err != nil {
		return nil, err
	}

	return svc.AttachDetail(ctx, order)
}

func (svc *orderService) ListTail(ctx context.Context, orderID int64) ([]*model.Order, error) {
	extraOrders, err := svc.orderRepo.ListTailOrder(ctx, orderID)
	if err != nil {
		return nil, err
	}

	return extraOrders, nil
}

// ListTailDetail lists all orders following the given orderID (which is the ID of the "head order").
func (svc *orderService) ListTailDetail(ctx context.Context, orderID int64) ([]*model.OrderDetail, error) {
	extraOrders, err := svc.ListTail(ctx, orderID)
	if err != nil {
		return nil, err
	}

	extraOrderDetails := make([]*model.OrderDetail, 0, len(extraOrders))

	for _, extraOrder := range extraOrders {
		extraOrderDetail, attachErr := svc.AttachDetail(ctx, extraOrder)
		if attachErr != nil {
			return nil, attachErr
		}

		extraOrderDetails = append(extraOrderDetails, extraOrderDetail)
	}

	return extraOrderDetails, nil
}

func (svc *orderService) ListByAppointment(ctx context.Context, appointmentID int64) (
	[]*model.Order, error,
) {
	return svc.orderRepo.ListByAppointment(ctx, appointmentID)
}

// ListDetailByAppointment lists all order related by given appointmentID.
func (svc *orderService) ListDetailByAppointment(ctx context.Context, appointmentID int64) (
	[]*model.OrderDetail, error,
) {
	orders, err := svc.orderRepo.ListByAppointment(ctx, appointmentID)
	if err != nil {
		return nil, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))

	for _, od := range orders {
		orderDetail, attachErr := svc.AttachDetail(ctx, od)
		if attachErr != nil {
			return nil, attachErr
		}

		orderDetails = append(orderDetails, orderDetail)
	}

	return orderDetails, nil
}

func (svc *orderService) ListDetailByFulfillment(
	ctx context.Context, fulfillmentID int64,
) ([]*model.OrderDetail, error) {
	orders, err := svc.orderRepo.ListByFulfillment(ctx, fulfillmentID)
	if err != nil {
		return nil, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))

	for _, od := range orders {
		orderDetail, attachErr := svc.AttachDetail(ctx, od)
		if attachErr != nil {
			return nil, attachErr
		}

		orderDetails = append(orderDetails, orderDetail)
	}

	return orderDetails, nil
}

// 由于历史设计，SourceType 是 NOSHOW，但是实际是从 grooming 模块创建的.
// 暂时无法通过参数区分真实来源，所以此接口只适用于 grooming 的 NoShow 订单.
// 在查询相应 deposit 时，会硬编码 sourceType=grooming + sourceId=sourceId 查询
func (svc *orderService) CreateNoShowOrder(ctx context.Context, req *ordersvcpb.CreateNoShowOrderRequest) (
	*model.OrderDetail, error,
) {
	const (
		orderTitle      = "No-show"
		itemName        = "No-show fee"
		defaultCurrency = "USD"
	)

	depositDetail, err := svc.getDepositDetailBySource(ctx, orderpb.OrderSourceType_APPOINTMENT, req.GetSourceId())
	if err != nil {
		return nil, err
	}

	amount := money.ToDecimal(req.GetNoShowFeeAmount())
	if amount.IsNegative() {
		// 前端不想要额外查询deposit balance，所以会直接传0，这里需要自行填充
		if depositDetail == nil || depositDetail.LatestChangeLog == nil {
			zlog.Error(
				ctx, "no deposit detail found",
				zap.Stringer("amount", amount),
				zap.Int64("sourceID", req.GetSourceId()),
				zap.Int64("sourceType", int64(orderpb.OrderSourceType_APPOINTMENT)),
			)

			return nil, status.Errorf(codes.InvalidArgument, "no deposit detail found")
		}

		amount = depositDetail.LatestChangeLog.Balance
	}

	items := []*model.OrderItem{
		{
			BusinessID:   req.GetBusinessId(),
			StaffID:      req.GetStaffId(),
			ItemType:     model.OrderItemTypeNoShow,
			Name:         itemName,
			UnitPrice:    amount,
			Quantity:     1,
			CurrencyCode: req.GetNoShowFeeAmount().GetCurrencyCode(),
			Tax: model.Tax{
				ID:     0,
				Amount: decimal.Zero,
			},
			SubTotalAmount: amount,
			TotalAmount:    amount,
		},
	}

	orderVersion := model.OrderVersionLegacy

	if isImmutable, err := svc.userFlagCli.ImmutableInvoice(ctx, req.GetCompanyId()); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to check user flag: %v", err)
	} else if isImmutable {
		zlog.Info(
			ctx, "company immutable",
			zap.Int64("companyID", req.GetCompanyId()),
			zap.Bool("isImmutable", isImmutable),
		)

		orderVersion = model.OrderVersionImmutableOrder
	} else {
		// 检查 Business 是否在 NewInvoice 白名单中.
		if isSwitched, err := svc.userFlagCli.SwitchedToNewInvoice(ctx, req.GetBusinessId()); err != nil {
			return nil, status.Errorf(codes.Internal, "failed to check user flag: %v", err)
		} else if isSwitched {
			zlog.Info(
				ctx, "business switched to new invoice",
				zap.Int64("businessID", req.GetBusinessId()),
				zap.Bool("isSwitched", isSwitched),
			)

			orderVersion = model.OrderVersionRefund
		}
	}

	zlog.Info(
		ctx, "order version",
		zap.Int64("companyID", req.GetCompanyId()),
		zap.Int64("businessID", req.GetBusinessId()),
		zap.Stringer("orderVersion", orderVersion),
	)

	order := model.NewEmptyOrder(
		orderpb.OrderModel_ORIGIN,
		req.GetSourceType(), req.GetSourceId(),
		req.GetCompanyId(), req.GetBusinessId(), req.GetCustomerId(), req.GetStaffId(),
	)

	order.CurrencyCode = req.GetNoShowFeeAmount().GetCurrencyCode()
	if order.CurrencyCode == "" { // grooming 没有传 currency code
		order.CurrencyCode = defaultCurrency
	}

	if depositDetail != nil && len(depositDetail.DepositChangeLogs) > 0 {
		orderVersion = model.OrderVersionImmutableOrder
	}

	engine := core.NewOrderEngine(order, items).WithDepositDetail(depositDetail)

	orderDetail := engine.PreviewOrderDetail()

	orderDetail.Order.OrderVersion = orderVersion
	orderDetail.Order.CreateBy = req.GetStaffId()
	orderDetail.Order.UpdateBy = req.GetStaffId()
	orderDetail.Order.Title = orderTitle
	orderDetail.Order.Description = req.GetDescription()
	orderDetail.Order.FulfillmentStatus = orderpb.OrderModel_COMPLETED.String()
	orderDetail.Order.LineItemTypes = 1 << (int(orderpb.ItemType_ITEM_TYPE_NO_SHOW) - 1)

	if orderDetail.DepositChangeLog != nil {
		orderDetail.DepositChangeLog.Reason = orderpb.DepositChangeReason_FORFEIT
	}

	txErr := svc.txRepo.Tx(
		func(tx repo.OrderTX) error {
			order := orderDetail.Order
			items := orderDetail.OrderItems

			orders, err := svc.orderRepo.ListByAppointmentForUpdate(ctx, req.GetSourceId())
			if err != nil {
				return err
			}

			// 同时只能有一个进行中的 NoShow order.
			for _, od := range orders {
				if od.IsNoShow() && !od.IsCanceled() {
					return status.Error(codes.AlreadyExists, "found another NoShow order")
				}
			}

			// 给 NoShow order 也挂上 Ref ID，便于 ListOrders 可以查到原单.
			// 也可能 NoShow 单就是第一单.
			order.OrderRefID = svc.getOrderRefID(ctx, orders)

			if err := tx.Order().Create(ctx, order); err != nil {
				return err
			}

			if len(items) > 0 {
				for _, item := range items {
					item.OrderID = order.ID
				}

				if err := tx.OrderItem().BatchCreate(ctx, items); err != nil {
					return err
				}
			}

			if orderDetail.DepositChangeLog != nil {
				if err := tx.DepositChangeLog().Create(ctx, orderDetail.DepositChangeLog); err != nil {
					return err
				}
			}

			return nil
		},
	)
	if txErr != nil {
		return nil, txErr
	}

	return &model.OrderDetail{
		Order:      orderDetail.Order,
		OrderItems: orderDetail.OrderItems,
	}, nil
}

func (svc *orderService) CancelOrder(ctx context.Context, orderID int64, cancelReason string) error {
	return svc.txRepo.Tx(
		func(tx repo.OrderTX) error {
			order, err := tx.Order().GetForUpdate(ctx, orderID)
			if err != nil {
				return err
			}

			if order.IsFinalStatus() {
				return status.Errorf(codes.FailedPrecondition, "order status is final status: %s", order.Status)
			}

			// Cancel order.
			_, err = tx.Order().CancelOrder(ctx, order)
			if err != nil {
				return err
			}

			// 创建一个订单消息，通知 Accounting.
			if eventErr := tx.MessageDeliveryRepo().CreateCanceled(ctx, order); eventErr != nil {
				return eventErr
			}

			// 老订单无 OrderPayment.
			if !order.SupportOrderPayment() {
				return nil
			}

			// Cancel 未完成的 Order payment.
			orderPayments, err := tx.OrderPayment().ListByOrderID(ctx, orderID)
			if err != nil {
				return err
			}

			// 过滤终态的 Order Payment.
			processingOrderPayments := lo.Filter(
				orderPayments,
				func(op *model.OrderPayment, _ int) bool { return !op.IsFinalState() },
			)

			for _, op := range processingOrderPayments {
				op.PaymentStatus = orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_CANCELED
				op.PaymentStatusReason = cancelReason

				_, err = tx.OrderPayment().CancelOrderPayment(ctx, op)
				if err != nil {
					return err
				}
			}

			return nil
		},
	)
}

// CreateInvoiceID 用途是在 check in 的时候，获取一个稳定的 invoice id.
// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
func (svc *orderService) CreateInvoiceID(
	ctx context.Context, req *ordersvcpb.CreateInvoiceIDRequest,
) (int64, error) {
	if req.GetSourceType() != orderpb.OrderSourceType_APPOINTMENT {
		return 0, status.Error(codes.InvalidArgument, "unsupported source type")
	}

	var invoiceID int64

	var depositBusinessID, depositInvoiceID int64 // 用于事务后调用

	txErr := svc.txRepo.Tx(
		func(tx repo.OrderTX) error {
			orders, err := tx.Order().ListByAppointmentForUpdate(ctx, req.GetSourceId())
			if err != nil {
				return err
			}

			// 有现成的.
			if len(orders) > 0 {
				invoiceID = svc.getOrderRefID(ctx, orders)

				// 记录第一个 deposit order 的 businessID/invoiceID，事务后调用
				for _, od := range orders {
					if od.IsDeposit() {
						depositBusinessID = od.BusinessID
						depositInvoiceID = od.ID

						break
					}
				}

				return nil
			}

			// 需要构造一个空订单用来承载.
			// 空订单需要用户不可见.
			emptyOrder := model.NewEmptyOrder(
				// 老逻辑（e.g Message）里面还有地方在查第一个 ORIGIN order。
				// 这里只能用 DEPOSIT 类型先顶了.
				orderpb.OrderModel_ORDER_TYPE_UNSPECIFIED,
				req.GetSourceType(), req.GetSourceId(),
				req.GetCompanyId(), req.GetBusinessId(), req.GetCustomerId(), req.GetStaffId(),
			)
			emptyOrder.OrderVersion = model.OrderVersionImmutableOrder
			// 加上特殊标记.
			emptyOrder.Title = model.OrderTitleFakeInvoice
			emptyOrder.Description = model.OrderTitleFakeInvoice
			// 设置为 REMOVED 状态，避免产生 side effect。
			emptyOrder.Status = orderpb.OrderStatus_REMOVED
			// Canceled order 都是 unpaid.
			emptyOrder.PaymentStatus = orderpb.OrderModel_UNPAID

			if createErr := tx.Order().Create(ctx, emptyOrder); createErr != nil {
				return createErr
			}

			invoiceID = emptyOrder.ID

			return nil
		},
	)
	if txErr != nil {
		return 0, txErr
	}

	// 事务提交成功后，若有 deposit order，异步调用 CaptureByInvoiceID，失败只打日志
	// 这里是为了在 checkin 时触发 capture，order中没有记录是否是 preauth 单，所以每一个order都调用一次，会在 payment 服务中进行检查
	if depositBusinessID > 0 && depositInvoiceID > 0 {
		go func(businessID, invoiceID int64) {
			err := svc.paymentClient.CaptureByInvoiceID(context.Background(), businessID, invoiceID)
			if err != nil {
				zlog.Error(
					ctx, "CaptureByInvoiceID failed",
					zap.Int64("businessID", businessID),
					zap.Int64("invoiceID", invoiceID),
					zap.Error(err),
				)
			}
		}(depositBusinessID, depositInvoiceID)
	}

	return invoiceID, nil
}

func (svc *orderService) AttachDetail(ctx context.Context, order *model.Order) (*model.OrderDetail, error) {
	items, err := svc.orderItemRepo.ListByOrder(ctx, order.ID)
	if err != nil {
		return nil, err
	}
	// attach staffs for service items from grooming pet details
	if order.IsAppointment() {
		if attachErr := helper.AttachStaff(ctx, svc.groomingClient, order, items); attachErr != nil {
			return nil, attachErr
		}
	}

	orderLineDiscounts, err := svc.orderLineDiscountRepo.ListByOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	// 过滤无效的数据.
	items = lo.Filter(
		items,
		func(it *model.OrderItem, _ int) bool { return !it.IsDeleted },
	)

	orderLineDiscounts = lo.Filter(
		orderLineDiscounts,
		func(it *model.OrderLineDiscount, _ int) bool { return !it.IsDeleted },
	)

	// 应用在 Order 层面的 Discount
	orderDiscount := lo.Filter(
		orderLineDiscounts,
		func(it *model.OrderLineDiscount, _ int) bool { return it.OrderItemID == 0 },
	)

	// 应用在 Item 层面的 Discount
	itemDiscount := lo.Filter(
		orderLineDiscounts,
		func(it *model.OrderLineDiscount, _ int) bool { return it.OrderItemID > 0 },
	)
	for _, discount := range itemDiscount {
		for _, item := range items {
			if discount.IsMatchedOrderItem(item) {
				item.ItemDiscounts = append(item.ItemDiscounts, discount)
			}
		}
	}

	orderDetail := &model.OrderDetail{
		Order:               order,
		OrderItems:          items,
		OrderPayments:       nil,
		RefundOrderPayments: nil,
		OrderDiscount:       orderDiscount,
	}

	if !order.SupportOrderPayment() {
		// 不支持 OrderPayment 的老订单
		return svc.attachPaymentForLegacyOrder(ctx, orderDetail)
	}

	// 附加 Order Payments.
	payments, err := svc.orderPaymentRepo.ListByOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	orderDetail.OrderPayments = payments

	// 获取订单的所有 Refund Order.
	refundOrders, err := svc.refundOrderRepo.ListDetailByOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	// Order 上只附加 关单前 的 Refund Payments 数据.
	refundOrdersBeforeCompleted := lo.Filter(
		refundOrders,
		func(rod *model.RefundOrderDetail, _ int) bool { return !rod.IsCreatedAfterCompleted() },
	)

	refundPaymentBeforeCompleted := make([]*model.RefundOrderPayment, 0, len(refundOrdersBeforeCompleted))
	for _, rod := range refundOrdersBeforeCompleted {
		refundPaymentBeforeCompleted = append(
			refundPaymentBeforeCompleted,
			rod.RefundOrderPayments...,
		)
	}

	// 给 Refund Order Payment 附带上对应的 Order Payment 的信息.
	idToOrderPayments := lo.KeyBy(
		orderDetail.OrderPayments,
		func(op *model.OrderPayment) int64 { return op.ID },
	)
	for _, refundPayment := range refundPaymentBeforeCompleted {
		refundPayment.RefundPaymentMethod.AttachOrderPayment(idToOrderPayments[refundPayment.OrderPaymentID])
	}

	orderDetail.RefundOrderPayments = refundPaymentBeforeCompleted

	depositDetail, err := svc.getDepositDetail(ctx, orderDetail.Order)
	if err != nil {
		return nil, err
	}

	orderDetail.Order.RefundableModes = svc.calculateRefundableModes(orderDetail, refundOrders, depositDetail)

	// 补充 promotion 信息
	promotionList, err := svc.getPromotions(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	orderDetail.OrderPromotions = promotionList

	return orderDetail, nil
}

func (svc *orderService) getPromotions(ctx context.Context, orderID int64) ([]*model.OrderPromotion, error) {
	promotionList, err := svc.orderPromotionRepo.ListByOrderID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	if len(promotionList) == 0 {
		return promotionList, nil
	}

	promotionItemList, err := svc.orderPromotionItemRepo.ListByPromotionIDs(ctx,
		lo.Map(promotionList, func(p *model.OrderPromotion, _ int) int64 {
			return p.ID
		}),
	)
	if err != nil {
		return nil, err
	}

	promotionIDToItems := lo.GroupBy(promotionItemList, func(pi *model.OrderPromotionItem) int64 {
		return pi.OrderPromotionID
	})
	for _, p := range promotionList {
		if itemList, ok := promotionIDToItems[p.ID]; ok {
			p.PromotionItems = itemList
		}
	}

	return promotionList, nil
}

func (svc *orderService) getDepositDetail(ctx context.Context, order *model.Order) (*model.DepositDetail, error) {
	if order.OrderType != orderpb.OrderModel_DEPOSIT {
		return nil, nil
	}

	changeLogs, err := svc.depositChangeLogRepo.ListByDepositOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	var latestChangeLog *model.DepositChangeLog
	if len(changeLogs) > 0 {
		latestChangeLog = changeLogs[0]
	}

	return &model.DepositDetail{
		DepositChangeLogs: changeLogs,
		LatestChangeLog:   latestChangeLog,
	}, nil
}

// 计算 Order 可用的退款模式
func (svc *orderService) calculateRefundableModes(
	orderDetail *model.OrderDetail, refundOrders []*model.RefundOrderDetail, depositDetail *model.DepositDetail,
) []orderpb.RefundMode {
	// 关单之前以及
	// Partial pay 强制关单的订单也只能用 By Payment 模式退款
	if !orderDetail.IsComplete() || orderDetail.IsPartialPaid() {
		return []orderpb.RefundMode{orderpb.RefundMode_REFUND_MODE_BY_PAYMENT}
	}

	// Deposit order 的 refundable modes 有特殊逻辑，优先判断
	if orderDetail.IsDeposit() {
		// 已经抵扣过的 deposit order，超付的部分必须 by item 先退完才能 by payment 退款；没有抵扣过的 deposit order 不受影响
		if _, found := lo.Find(
			depositDetail.DepositChangeLogs,
			func(it *model.DepositChangeLog) bool { return it.Reason == orderpb.DepositChangeReason_DEDUCTION },
		); found && depositDetail.LatestChangeLog.Balance.IsPositive() {
			return []orderpb.RefundMode{orderpb.RefundMode_REFUND_MODE_BY_ITEM}
		}

		// Deposit order 就算 refund by payment 最终也是 refund by item，所以允许 by item 和 by payment 混用
		return []orderpb.RefundMode{orderpb.RefundMode_REFUND_MODE_BY_ITEM, orderpb.RefundMode_REFUND_MODE_BY_PAYMENT}
	}

	// 关单后不可混用
	refundOrdersAfterCompleted := lo.Filter(
		refundOrders,
		func(it *model.RefundOrderDetail, _ int) bool { return it.IsCreatedAfterCompleted() },
	)

	if len(refundOrdersAfterCompleted) == 0 {
		// 关单后没有退过，所有模式都可以
		return []orderpb.RefundMode{
			orderpb.RefundMode_REFUND_MODE_BY_ITEM,
			orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		}
	}

	// 只能用退过的模式
	return []orderpb.RefundMode{
		refundOrdersAfterCompleted[0].RefundOrder.RefundMode,
	}
}

func (svc *orderService) attachPaymentForLegacyOrder(ctx context.Context, orderDetail *model.OrderDetail) (
	*model.OrderDetail, error,
) {
	// 老版本的订单直接读 Payment 并转换成 OrderPayment
	payments, err := svc.legacyPaymentRepo.ListByOrderID(ctx, orderDetail.GetID())
	if err != nil {
		return nil, err
	}

	orderDetail.OrderPayments = payments

	// 老版本的订单直接读 Refund 并转换成 RefundOrderPayment
	refundPayments, err := svc.legacyRefundRepo.ListByOrderID(ctx, orderDetail.GetID())
	if err != nil {
		return nil, err
	}

	// 老版本的订单没有 RefundOrder。如果在正向订单中也不展示关单后的退款，会导致这部分记录在用户侧完全不可见.
	// 所以老订单不做过滤.
	orderDetail.RefundOrderPayments = refundPayments

	// 老订单的 RefundPayment 缺少了非常多的信息，都需要从对应的 Payment 中补.
	// 另外 Payment 也没有记录已退的金额，需要从 RefundPayment 中集合计算.
	idToPayment := lo.KeyBy(payments, func(it *model.OrderPayment) int64 { return it.ID })
	for _, rp := range refundPayments {
		op, ok := idToPayment[rp.OrderPaymentID]
		if !ok {
			continue
		}

		op.RefundedAmount = op.GetRefundedAmount().Add(rp.GetRefundAmount())
		op.RefundedConvenienceFee = op.GetRefundedConvenienceFee().Add(rp.GetRefundConvenienceFee())

		rp.RefundPaymentMethod.ID = op.PaymentMethod.ID
		rp.RefundPaymentMethod.Method = op.PaymentMethod.Method
		rp.RefundPaymentMethod.AttachOrderPayment(op)
	}

	// 老订单固定只支持 By Payment 退款
	orderDetail.Order.RefundableModes = []orderpb.RefundMode{orderpb.RefundMode_REFUND_MODE_BY_PAYMENT}

	return orderDetail, nil
}

func (svc *orderService) getOrderRefID(_ context.Context, orders []*model.Order) int64 {
	for _, od := range orders {
		if od.OrderRefID > 0 {
			return od.OrderRefID
		}

		if od.ID > 0 {
			return od.ID
		}
	}

	return 0
}

func (svc *orderService) getDepositDetailBySource(
	ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64,
) (*model.DepositDetail, error) {
	if sourceType != orderpb.OrderSourceType_APPOINTMENT || sourceID <= 0 {
		return nil, nil
	}

	orders, err := svc.ListByAppointment(ctx, sourceID)
	if err != nil {
		return nil, err
	}

	depositOrder, ok := lo.Find(
		orders,
		func(order *model.Order) bool { return order.OrderType == orderpb.OrderModel_DEPOSIT },
	)
	if !ok {
		return nil, nil
	}

	changeLogs, err := svc.depositChangeLogRepo.ListByDepositOrderID(ctx, depositOrder.ID)
	if err != nil {
		return nil, err
	}

	var latestChangeLog *model.DepositChangeLog
	if len(changeLogs) > 0 {
		latestChangeLog = changeLogs[0]
	}

	return &model.DepositDetail{
		DepositChangeLogs: changeLogs,
		LatestChangeLog:   latestChangeLog,
	}, nil
}
