// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"

	"github.com/growthbook/growthbook-golang"

	"github.com/MoeGolibrary/go-lib/gorm"
	"github.com/MoeGolibrary/go-lib/grpc"
	"github.com/MoeGolibrary/go-lib/redis"
	"github.com/MoeGolibrary/go-lib/server"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	ordersvcpb2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	ordersvcpb3 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/controller"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/business"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/depositrule"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/promotion"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/subscription"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service/helper"
)

// Injectors from wire.go:

func NewServer() server.Server {
	db := newOrderDB()
	orderRepo := repo.NewOrderRepo(db)
	orderItemRepo := repo.NewOrderItemRepo(db)
	orderPaymentRepo := repo.NewOrderPaymentRepo(db)
	orderLineDiscountRepo := repo.NewOrderLineDiscountRepo(db)
	refundOrderPaymentRepo := repo.NewRefundOrderPaymentRepo(db)
	refundOrderRepo := repo.NewRefundOrderRepo(db)
	depositChangeLogRepo := repo.NewDepositChangeLogRepoRepo(db)
	orderPromotionRepo := repo.NewOrderPromotionRepo(db)
	orderPromotionItemRepo := repo.NewOrderPromotionItemRepo(db)
	txRepo := repo.NewTXRepo(db)
	v := newRedisClient()
	client := newGrowthBookClient()
	userFlagClient := repo.NewUserFlagClient(v, client)
	paymentDB := newPaymentDB()
	legacyPaymentRepo := repo.NewLegacyPaymentRepo(paymentDB)
	legacyRefundRepo := repo.NewLegacyRefundRepo(paymentDB)
	groomingClient := grooming.NewGroomingClient()
	paymentClient := repo.NewPaymentClient()
	orderService := service.NewOrderService(orderRepo, orderItemRepo, orderPaymentRepo, orderLineDiscountRepo, refundOrderPaymentRepo, refundOrderRepo, depositChangeLogRepo, orderPromotionRepo, orderPromotionItemRepo, txRepo, userFlagClient, legacyPaymentRepo, legacyRefundRepo, groomingClient, paymentClient)
	refundOrderItemRepo := repo.NewRefundOrderItemRepo(db)
	refundOrderService := service.NewRefundOrderService(orderRepo, orderItemRepo, orderPaymentRepo, refundOrderRepo, refundOrderItemRepo, refundOrderPaymentRepo, depositChangeLogRepo, txRepo, paymentClient, groomingClient)
	depositOrderService := service.NewDepositOrderService(depositChangeLogRepo, orderRepo, txRepo)
	discountCodeServiceClient := newDiscountClient()
	tipsSplitRepo := repo.NewTipsSplitRepo(db)
	tipsSplitDetailRepo := repo.NewTipsSplitDetailRepo(db)
	legacyOrderTipsSplitDetailRepo := repo.NewLegacyOrderTipsSplitDetailRepo(db)
	txTipsSplitRepo := repo.NewTxTipsSplitRepo(db)
	tipsSplitEngine := helper.NewTipsSplitEngine()
	businessClient := business.NewBusinessClient()
	tipsSplitStatusClient := repo.NewTipsSplitStatusClient(v)
	tipsSplitService := service.NewTipsSplitService(orderRepo, orderItemRepo, tipsSplitRepo, tipsSplitDetailRepo, legacyOrderTipsSplitDetailRepo, txTipsSplitRepo, tipsSplitEngine, businessClient, groomingClient, tipsSplitStatusClient)
	orderServiceServer := controller.NewOrderServer(orderService, refundOrderService, depositOrderService, discountCodeServiceClient, tipsSplitService)
	promotionClient := promotion.New()
	subscriptionClient := subscription.New()
	promotionService := service.NewPromotionService(promotionClient, subscriptionClient, orderPromotionRepo, orderPromotionItemRepo, orderRepo)
	ordersvcpbOrderServiceServer := controller.NewOrderServerV2(promotionService, depositOrderService, orderService)
	splitTipsServiceServer := controller.NewTipsSplitServiceServer(tipsSplitService, orderService, refundOrderService)
	orderItemAssignedAmountRepo := repo.NewOrderItemAssignedAmountRepo(db)
	itemAssignService := service.NewItemAssignService(orderItemAssignedAmountRepo)
	assignItemAmountServiceServer := controller.NewAssignItemAmountServer(itemAssignService)
	depositruleRepo := depositrule.NewDepositRuleRepo(db)
	organizationRepo := depositrule.NewBusinessRepo()
	customerRepo := depositrule.NewCustomerRepo()
	depositRuleService := service.NewDepositRulesService(depositruleRepo, organizationRepo, customerRepo, paymentClient, groomingClient)
	depositRuleServiceServer := controller.NewDepositRuleServer(depositRuleService)
	orderTaskServiceServer := controller.NewOrderTaskServer(promotionService, orderService)
	serverServer := newServer(orderServiceServer, ordersvcpbOrderServiceServer, splitTipsServiceServer, assignItemAmountServiceServer, depositRuleServiceServer, orderTaskServiceServer)

	return serverServer
}

// wire.go:

func newServer(
	serviceServer ordersvcpb.OrderServiceServer,
	serviceServerV2 ordersvcpb2.OrderServiceServer,
	tipsSplitServer ordersvcpb2.SplitTipsServiceServer,
	assignItemAmountServer ordersvcpb3.AssignItemAmountServiceServer,
	depositRuleServer ordersvcpb2.DepositRuleServiceServer,
	orderTaskServer ordersvcpb2.OrderTaskServiceServer,
) server.Server {
	s := server.NewDefaultServer()
	ordersvcpb.RegisterOrderServiceServer(s, serviceServer)
	ordersvcpb3.RegisterAssignItemAmountServiceServer(s, assignItemAmountServer)
	ordersvcpb2.RegisterSplitTipsServiceServer(s, tipsSplitServer)
	ordersvcpb2.RegisterDepositRuleServiceServer(s, depositRuleServer)
	ordersvcpb2.RegisterOrderServiceServer(s, serviceServerV2)
	ordersvcpb2.RegisterOrderTaskServiceServer(s, orderTaskServer)

	return s
}

func newOrderDB() *gorm.DB {
	return gorm.Must(gorm.OpenPostgres(config.OrderDSN(), nil))
}

func newPaymentDB() *repo.PaymentDB {
	return (*repo.PaymentDB)(gorm.Must(gorm.OpenMySQL(config.PaymentDSN(), nil)))
}

func newRedisClient() redis.UniversalClient { return redis.NewClient(config.Redis()) }

func newDiscountClient() marketingsvcpb.DiscountCodeServiceClient {
	return grpc.NewClient("moego-svc-marketing:9090", marketingsvcpb.NewDiscountCodeServiceClient)
}

func newGrowthBookClient() *growthbook.Client {
	gb, err := growthbook.NewClient(context.Background(), config.GrowthBook()...)
	if err != nil {
		panic(err)
	}

	return gb
}
